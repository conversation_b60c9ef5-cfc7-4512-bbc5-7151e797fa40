<%@ page import="java.sql.*, java.util.*, java.io.*" %>
<%@ page contentType="text/plain;charset=UTF-8" language="java" %>

<%
    // 1️⃣ 获取 URL 参数
    String demographicNo = request.getParameter("demographic_no");
    String appointmentNo = request.getParameter("appointment");

    if (demographicNo == null || appointmentNo == null) {
        out.print("Error: Missing parameters.");
        return;
    }

    // 2️⃣ 定义变量（待查询的患者信息）
    String patientName = "";
    String patientEmail = "";
    String appointmentType = "";
    String appointmentDate = "";
    String startTime = "";
    String provider = "";
    String patientPhone = "";
    String patientSex = "";
    String patientBirthday = "";
    String patientHin = "";

    Connection conn = null;
    PreparedStatement stmt = null;
    ResultSet rs = null;

    try {
        // 3️⃣ 连接数据库
        Class.forName("com.mysql.cj.jdbc.Driver");
        conn = DriverManager.getConnection("**************************", "root", "Z2Rh6VGr7DE=");

        // 4️⃣ 查询预约信息
        String sql = "SELECT d.email, a.type, a.appointment_date, a.start_time, d.hin, concat(d.year_of_birth, '-', d.month_of_birth, '-', d.date_of_birth) as birthday, " +
                     "p.last_name, p.first_name, a.name, d.phone, d.sex " +
                     "FROM appointment a " +
                     "JOIN demographic d ON d.demographic_no = a.demographic_no " +
                     "JOIN provider p ON p.provider_no = a.provider_no " +
                     "WHERE a.appointment_no= ?";

        stmt = conn.prepareStatement(sql);
        stmt.setString(1, appointmentNo);
        rs = stmt.executeQuery();

        if (rs.next()) {
            patientName = rs.getString("name");
            patientEmail = rs.getString("email");
            appointmentType = rs.getString("type");
            appointmentDate = rs.getString("appointment_date");
            startTime = rs.getString("start_time");
            provider = rs.getString("first_name") + " " + rs.getString("last_name");
            patientPhone = rs.getString("phone");
            patientSex = rs.getString("sex");
            patientBirthday = rs.getString("birthday");
            patientHin = rs.getString("hin");
        } else {
            out.print("Error: Patient or provider not found.");
            return;
        }
    } catch (Exception e) {
        out.print("Database error: " + e.getMessage());
        return;
    } finally {
        if (rs != null) rs.close();
        if (stmt != null) stmt.close();
        if (conn != null) conn.close();
    }

    // 5️⃣ 构建 Python 命令
    String pythonScriptPath = "/usr/local/tomcat/webapps/oscar/reminder/reminder.py"; // Python 脚本路径
    String command = "python3 " + pythonScriptPath + " \"" + patientEmail + "\" \"" + provider + "\" \"" +
                     patientName + "\" \"" + appointmentType + "\" \"" + appointmentDate + "\" \"" + startTime + "\"" + " \"" + patientPhone + "\" \"" +
                     patientSex + "\" \"" + patientBirthday + "\" \"" + patientHin + "\"";

    try {
        // 6️⃣ 执行 Python 命令
        Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", command});
        int exitCode = process.waitFor();

        if (exitCode == 0) {
            out.print("✅ Email successfully sent to " + patientEmail);
        } else {
            out.print("❌ Email sending failed. Exit code: " + exitCode);
        }
    } catch (Exception e) {
        out.print("❌ Error executing Python script: " + e.getMessage());
    }
%>
