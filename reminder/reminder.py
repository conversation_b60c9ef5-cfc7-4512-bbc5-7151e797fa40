import smtplib
import ssl
import os
import sys
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

# **📌 SMTP Configuration**
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "guczjkeuysxklqwg"

# **📌 Get parameters from JSP**
try:
    recipient_email = sys.argv[1]  # Email recipient
    provider = sys.argv[2]  # Doctor's name
    patient_name = sys.argv[3]  # Patient Name
    appt_type = sys.argv[4]  # Appointment Type
    appt_date = sys.argv[5]  # Appointment Date
    start_time = sys.argv[6]  # Appointment Time
    patient_phone = sys.argv[7]  # Patient Phone
    patient_sex = sys.argv[8]  # Patient Sex
    patient_dob = sys.argv[9]  # Patient Date of Birth
    patient_hin = sys.argv[10]  # Patient Health Insurance Number
except IndexError:
    print("❌ Error: Missing parameters in script execution.")
    sys.exit(1)

clinic_name = "Midtown Medical Clinic"
clinic_address = "130-8780 Blundell Rd, Richmond, BC V6Y 3Y8"

# **📌 Attachments**
attachments = {
    "Ken_36915 Sun": "/usr/local/tomcat/webapps/oscar/reminder/Instruction-Spirometry.pdf",
    "Harry CHAOCHENG_J6097_49203 Liu": None,
    "YanXiang_59539 Gao": None,
    "Joshua_J6423 Lee": None
}

# **📌 Email Templates**
email_templates = {
    "Ken_36915 Sun": {
        "subject": "Please reply with 'CONFIRMED' for your spirometry test appointment.",
        "body": f"""
        <div style='font-family: Arial, sans-serif; color: #333;'>
            <p>Hi {patient_name},</p>
            <hr>
            <p>Birthday: {patient_dob}</p>
            <p>Phone: {patient_phone}</p>
            <P>Care Card: {patient_hin}</p>
            <p>Gender: {patient_sex}</p>
            <hr>
            <p>Thank you for choosing us for your healthcare needs. You are scheduled for a spirometry test with Dr. Ken Sun as follows:</p>
            <ul>
                <li><strong>Appointment Date:</strong> {appt_date}</li>
                <li><strong>Appointment Time:</strong> {start_time}</li>
                <li><strong>Clinic:</strong> {clinic_name}</li>
                <li><strong>Address:</strong> {clinic_address}</li>
            </ul>
            <p>Please take note of the following important details:</p>
            <ol>
                <li>Please arrive at the clinic 10 minutes before your appointment time for registration and ID verification.</li>
                <li>Please bring your MSP card with you to the clinic.</li>
                <li>Continue to take all your non-respiratory medications prescribed by your doctor.</li>
                <li>Bring your own Oxygen device (e.g., aerochamber, if applicable).</li>
                <li>It will take 30-45 mins for this appointment.</li>
                <li>Parking is free and available at designated slots outside the clinic.</li>
                <li>Please follow up with your referring doctor or clinic for the report.</li>
                <li>Our office requires 48-hour notice for rescheduling or cancellation. Any No-Show will be charged $50.</li>
                <li>If you need to reschedule your appointment for a different test time, please email us.</li>
                <li>Please reply with 'CONFIRMED' to this email, along with your Height (cm) and Weight (kg) at least 3 business days prior to your appointment.</li>
                <li>Please carefully review the attached instructions. If you have any questions, please feel free to contact us via email.</li>
            </ol>
            <p>Warm regards,<br>{clinic_name} Specialist Team</p>
        </div>
        """
    },
    "Harry CHAOCHENG_J6097_49203 Liu": {
        "subject": "Dermatologist Appointment: Please reply with 'CONFIRMED'",
        "body": f"""
        <div style='font-family: Arial, sans-serif; color: #333;'>
            <p>Hi {patient_name},</p>
            <hr>
            <p>Birthday: {patient_dob}</p>
            <p>Phone: {patient_phone}</p>
            <P>Care Card: {patient_hin}</p>
            <p>Gender: {patient_sex}</p>
            <hr>
            <p>Thank you for letting us be involved in your dermatology care.</p>
            <p>You are scheduled for an appointment with Dr. Harry Liu for:</p>
            <ul>
                <li><strong>Appointment Date:</strong> {appt_date}</li>
                <li><strong>Appointment Time:</strong> {start_time}</li>
                <li><strong>Clinic:</strong> {clinic_name}</li>
                <li><strong>Address:</strong> {clinic_address}</li>
            </ul>
            <p>Important information:</p>
            <ol>
                <li>Please arrive 5-10 minutes early to complete the intake form.If you have difficulty reading small print, please bring reading glasses or ask family or friend to assist with completing forms.</li>
                <li>Bring your Care Card and an updated list of current medications.</li>
                <li>Only **one issue** will be addressed per visit.</li>
                <li>Wear loose-fitting clothes for easier skin examination.</li>
                <li>Interpreter needed if you don't speak English or Mandarin fluently. </li>
                <li>48-hour notice required for cancellations or reschedules. Same-day cancellation and No-Shows will be charged **$75**.</li>
                <li>The entire appointment, including form completion, typically takes 20–25 minutes. However, due to potential emergencies or unforeseen delays, please allow at least  30–45 minute buffer.</li>
                <li>Doctor will see patients based on their scheduled appointment times. We are unable to adjust for earlier or later times on the same day.</li>
                <li>Please reply with 'CONFIRMED' at least 5 business days before your appointment.</li>
            </ol>
            <p>Warm regards,<br>{clinic_name} Specialist Team</p>
        </div>
        """
    },
    "YanXiang_59539 Gao": {
        "subject": "MMC Dr. Gao Appointment: Please reply with 'CONFIRMED'",
        "body": f"""
        <div style='font-family: Arial, sans-serif; color: #333;'>
            <p>Hi {patient_name},</p>
            <hr>
            <p>Birthday: {patient_dob}</p>
            <p>Phone: {patient_phone}</p>
            <P>Care Card: {patient_hin}</p>
            <p>Gender: {patient_sex}</p>
            <hr>
            <p>Thank you for letting us be involved in your gynecological care.</p>
            <p>You are scheduled for an appointment with Dr. YanXiang Gao for:</p>
            <ul>
                <li><strong>Appointment Type:</strong> {appt_type}</li>
                <li><strong>Appointment Date:</strong> {appt_date}</li>
                <li><strong>Appointment Time:</strong> {start_time}</li>
                <li><strong>Clinic:</strong> {clinic_name}</li>
                <li><strong>Address:</strong> {clinic_address}</li>
            </ul>
            <p>Important information:</p>
            <ol>
                <li>We are located at 130-8780 Blundell Rd, Richmond, BC, V6Y 3Y8. There are reserve parking lots available for our clinic patients.</li>
                <li>Our office requires 48-hour notice for appointment rescheduling or cancellation. Any same-day cancellation or No-Show will be charged $75.</li>
                <li>The initial appointment will take approximately 15 minutes and please arrive 5~10 mins; if you do not speak English or Mandarin fluently, please make sure that you bring an interpreter.</li>
                <li>Please be advised that the Doctor could address ONE issue during each visit.</li>
                <li>Appointment is in person, please bring your CareCard and arrive 5–10 minutes early.</li>
                <li>If your appointment is a phone consultation, the doctor will call you within 30 minutes before or after your scheduled time. The call may come from our clinic number or appear as No Caller ID.</li>
                <li>Please wear loose fitting clothes for easier exposure of your skin if you would like the doctor to examine your concerns.</li>
                <li>Please reply with 'CONFIRMED' to this email,ensure you do so at least 5 business days prior to your appointment.</li>
            </ol>
            <p>Warm regards,<br>{clinic_name} Specialist Team</p>
        </div>
        """
    },
    "DOC_C4152 MMC (HM)": {
        "subject": "MMC Dr. Gao Appointment: Please reply with 'CONFIRMED'",
        "body": f"""
        <div style='font-family: Arial, sans-serif; color: #333;'>
            <p>Hi {patient_name},</p>
            <hr>
            <p>You are scheduled for an appointment with Dr. YanXiang Gao for:</p>
            <ul>
                <li><strong>Appointment Type:</strong> {appt_type}</li>
                <li><strong>Appointment Date:</strong> {appt_date}</li>
                <li><strong>Appointment Time:</strong> {start_time}</li>
                <li><strong>Clinic:</strong> {clinic_name}</li>
                <li><strong>Address:</strong> {clinic_address}</li> 
            </ul>
            <p>Please bring your Care Card and arrive 5-10 minutes early.</p>
            <p>Warm regards,<br>{clinic_name} Specialist Team</p>
        </div>
        """
    },
    "Joshua_J6423 Lee": {
        "subject": "MMC Dr. Lee Zoom Meeting Appointment: Please reply with 'CONFIRMED'",
        "body": f"""
        <div style='font-family: Arial, sans-serif; color: #333;'>
            <p>Hi {patient_name},</p>
            <hr>
            <p>Birthday: {patient_dob}</p>
            <p>Phone: {patient_phone}</p>
            <p>Care Card: {patient_hin}</p>
            <p>Gender: {patient_sex}</p>
            <hr>
            <p>Thank you for letting us be involved in your mental health care.</p>
            <p>You are scheduled for an appointment with Dr. Lee for:</p>
            <ul>
                <li><strong>Appointment Date:</strong> {appt_date}</li>
                <li><strong>Zoom Meeting Appointment Time:</strong> {start_time}</li>
            </ul>
            <p><strong>Important information:</strong></p>
            <ol>
                <li>Dr. Lee will conduct the consultation via Zoom. Here is the link for a virtual health meeting with Dr. Lee.</li>
                <li><strong>Zoom Link:</strong> <a href="https://phsa.zoom.us/s/64118422145?pwd=N41Fs2w1LpaJ6p6Lb0jAM4bMYXd3Ne.1#success">https://phsa.zoom.us/s/64118422145?pwd=N41Fs2w1LpaJ6p6Lb0jAM4bMYXd3Ne.1#success</a> | <strong>Passcode:</strong> 271242</li>
                <li>Please be ready and click the link 5 minutes before your scheduled time. If you have not used Zoom before, we recommend that you download and set up the software in advance and try clicking the link to ensure it works.</li>
                <li>Our office requires 48-hour notice for appointment rescheduling or cancellation. Any same-day cancellation or No-Show will be charged $75.</li>
                <li>Dr. Lee may send you some forms to complete(after 1st initial appt). Please fill them out and return them via email before the appointment.</li>
                <li>Please reply with 'CONFIRMED' to this email, ensure you do so at least 3 business days prior to your appointment.</li>
                <li>If you have any questions or need further assistance, please do not hesitate to contact us. Thank you for your cooperation!</li>
            </ol>
            <p>Warm regards,<br>{clinic_name} Specialist Team</p>
        </div>
        """
    },
    "default": {
        "subject": "Your Appointment Confirmation",
        "body": f"""
        <div style='font-family: Arial, sans-serif; color: #333;'>
            <p>Hi {patient_name},</p>
            <hr>
            <p>Birthday: {patient_dob}</p>
            <p>Phone: {patient_phone}</p>
            <P>Care Card: {patient_hin}</p>
            <p>Gender: {patient_sex}</p>
            <hr>
            <p>You are scheduled for an appointment at {clinic_name}.</p>
            <ul>
                <li><strong>Appointment Date:</strong> {appt_date}</li>
                <li><strong>Appointment Time:</strong> {start_time}</li>
                <li><strong>Clinic:</strong> {clinic_name}</li>
                <li><strong>Address:</strong> {clinic_address}</li>
            </ul>
            <p>Please bring your Care Card and arrive 5-10 minutes early.</p>
            <p>Warm regards,<br>{clinic_name} Specialist Team</p>
        </div>
        """
    }
}

# **📌 Select the appropriate template**
selected_template = email_templates.get(provider, email_templates["default"])

# **📌 Create Email**
msg = MIMEMultipart()
msg["From"] = SENDER_EMAIL
msg["To"] = recipient_email
msg["Subject"] = selected_template["subject"]

# **📌 Attach HTML body**
msg.attach(MIMEText(selected_template["body"], "html"))

# **📌 Attach PDF (if exists)**
pdf_path = attachments.get(provider, None)
if pdf_path and os.path.exists(pdf_path):
    with open(pdf_path, "rb") as attachment:
        part = MIMEBase("application", "octet-stream")
        part.set_payload(attachment.read())
        encoders.encode_base64(part)
        part.add_header("Content-Disposition", f"attachment; filename={os.path.basename(pdf_path)}")
        msg.attach(part)

# **📌 Send Email**
context = ssl.create_default_context()
server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
server.starttls(context=context)
server.login(SENDER_EMAIL, SENDER_PASSWORD)
server.sendmail(SENDER_EMAIL, recipient_email, msg.as_string())
server.quit()

print("✅ Email successfully sent!")
