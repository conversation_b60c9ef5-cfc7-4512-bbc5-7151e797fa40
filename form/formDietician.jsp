<%--

    Copyright (c) 2006-. OSCARservice, OpenSoft System. All Rights Reserved.
    This software is published under the GPL GNU General Public License.
    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.

--%>
<%@ taglib uri="/WEB-INF/security.tld" prefix="security"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ page import="oscar.util.*, oscar.form.*, oscar.form.data.*, java.util.Properties, org.oscarehr.util.LoggedInInfo, java.sql.Connection, java.sql.PreparedStatement, java.sql.ResultSet, java.sql.Statement, java.text.SimpleDateFormat" %>

<sql:setDataSource var="oscarDB" dataSource="jdbc/CustomOscarDB" />

<% // --- START OF SERVER-SIDE LOGIC --- %>
<%
    String roleName2$ = (String)session.getAttribute("userrole") + "," + (String) session.getAttribute("user");
    boolean authed=true;
%>
<security:oscarSec roleName="<%=roleName2$%>" objectName="_form" rights="r" reverse="<%=true%>">
	<%authed=false; %>
	<%response.sendRedirect("../securityError.jsp?type=_form");%>
</security:oscarSec>
<%
	if(!authed) {
		return; // Exit if not authorized
	}

    // Initial parameters
    int demoNo = 0;
    String demoNoParam = request.getParameter("demographic_no");
    if (demoNoParam != null && !demoNoParam.isEmpty()) {
        try {
            demoNo = Integer.parseInt(demoNoParam);
        } catch (NumberFormatException e) {
            // Handle error - demoNo is crucial
            out.println("Error: Invalid demographic_no.");
            return;
        }
    }

    String formIdParam = request.getParameter("formId");
    int formId = (formIdParam == null || formIdParam.isEmpty() || "0".equals(formIdParam)) ? 0 : Integer.parseInt(formIdParam);
    
    int provNo = 0;
    String provNoParam = (String) session.getAttribute("user");
     if (provNoParam != null && !provNoParam.isEmpty()) {
        try {
            provNo = Integer.parseInt(provNoParam);
        } catch (NumberFormatException e) {
            out.println("Error: Invalid provider number in session.");
            return;
        }
    }


    String formAction = request.getParameter("formAction");
    String submitButtonValue = request.getParameter("submit"); // From original save & exit

    Properties props = new Properties();
    String formClass = "Dietician";
    String formLink = "formDietician.jsp";
    String project_home = request.getContextPath().length() > 1 ? request.getContextPath().substring(1) : "";

    boolean recordJustSaved = false;
    boolean saveSuccess = false;

    if (("save".equals(formAction)) || ("exit".equals(submitButtonValue) && "Save and Exit".equals(request.getParameter("saveexitbut")) )) {
        String p_doc_name = request.getParameter("doc_name");
        String p_cl_name = request.getParameter("cl_name");
        String p_cl_address1 = request.getParameter("cl_address1");
        String p_cl_address2 = request.getParameter("cl_address2");
        String p_cl_phone = request.getParameter("cl_phone");
        String p_cl_fax = request.getParameter("cl_fax");
        String p_consultTime_str = request.getParameter("consultTime");
        String p_comments = request.getParameter("comments");

        String p_p_name = request.getParameter("p_name");
        String p_p_address1 = request.getParameter("p_address1");
        String p_p_address2 = request.getParameter("p_address2");
        String p_p_phone = request.getParameter("p_phone");
        String p_p_birthdate = request.getParameter("p_birthdate");
        String p_p_healthcard = request.getParameter("p_healthcard");

        String sqlConsultTime = null;
        if (p_consultTime_str != null && !p_consultTime_str.isEmpty()) {
            try {
                SimpleDateFormat sdfSource = new SimpleDateFormat("yyyy/MM/dd");
                java.util.Date utilDate = sdfSource.parse(p_consultTime_str);
                SimpleDateFormat sdfDest = new SimpleDateFormat("yyyy-MM-dd");
                sqlConsultTime = sdfDest.format(utilDate);
            } catch (java.text.ParseException e) {
                // Handle invalid date format if necessary
                 out.println("<!-- Error parsing consultTime: " + e.getMessage() + "-->");
                 sqlConsultTime = null; // Or set to a default/error indicator
            }
        }

        pageContext.setAttribute("current_provider_no", provNo);
        pageContext.setAttribute("current_doc_name", p_doc_name);
        pageContext.setAttribute("current_cl_name", p_cl_name);
        pageContext.setAttribute("current_cl_address1", p_cl_address1);
        pageContext.setAttribute("current_cl_address2", p_cl_address2);
        pageContext.setAttribute("current_cl_phone", p_cl_phone);
        pageContext.setAttribute("current_cl_fax", p_cl_fax);
        pageContext.setAttribute("current_demographic_no", demoNo);
        pageContext.setAttribute("current_p_name", p_p_name);
        pageContext.setAttribute("current_p_address1", p_p_address1);
        pageContext.setAttribute("current_p_address2", p_p_address2);
        pageContext.setAttribute("current_p_phone", p_p_phone);
        pageContext.setAttribute("current_p_birthdate", p_p_birthdate);
        pageContext.setAttribute("current_p_healthcard", p_p_healthcard);
        pageContext.setAttribute("current_comments", p_comments);
        pageContext.setAttribute("current_consultTime", sqlConsultTime); // Already yyyy-MM-dd
        pageContext.setAttribute("current_formId_for_update", formId);


        if (formId == 0) {
%>
            <sql:update dataSource="${oscarDB}" var="insertCount">
                INSERT INTO formDietician (provider_no, doc_name, cl_name, cl_address1, cl_address2, cl_phone, cl_fax, demographic_no, p_name, p_address1, p_address2, p_phone, p_birthdate, p_healthcard, comments, consultTime, formCreated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())
                <sql:param value="${current_provider_no}" /> <sql:param value="${current_doc_name}" /> <sql:param value="${current_cl_name}" /> <sql:param value="${current_cl_address1}" /> <sql:param value="${current_cl_address2}" /> <sql:param value="${current_cl_phone}" /> <sql:param value="${current_cl_fax}" />
                <sql:param value="${current_demographic_no}" /> <sql:param value="${current_p_name}" /> <sql:param value="${current_p_address1}" /> <sql:param value="${current_p_address2}" /> <sql:param value="${current_p_phone}" /> <sql:param value="${current_p_birthdate}" /> <sql:param value="${current_p_healthcard}" />
                <sql:param value="${current_comments}" /> <sql:param value="${current_consultTime}" />
            </sql:update>
<%
            saveSuccess = ((Integer)pageContext.getAttribute("insertCount") > 0);
            if (saveSuccess) {
                Connection conn = null; PreparedStatement ps = null; ResultSet rs = null;
                try {
                    javax.sql.DataSource ds = (javax.sql.DataSource) pageContext.findAttribute("oscarDB");
                    conn = ds.getConnection();
                    ps = conn.prepareStatement("SELECT MAX(ID) AS newId FROM formDietician WHERE demographic_no = ? AND provider_no = ?");
                    ps.setInt(1, demoNo); ps.setInt(2, provNo);
                    rs = ps.executeQuery();
                    if (rs.next()) { formId = rs.getInt("newId"); }
                } catch (Exception e) { out.println("<!-- Error getting new form ID: " + e.getMessage() + "-->"); e.printStackTrace(new java.io.PrintWriter(out));}
                finally { if (rs != null) rs.close(); if (ps != null) ps.close(); if (conn != null) conn.close(); }
            }
        } else {
%>
            <sql:update dataSource="${oscarDB}" var="updateCount">
                UPDATE formDietician SET
                provider_no = ?, doc_name = ?, cl_name = ?, cl_address1 = ?, cl_address2 = ?, cl_phone = ?, cl_fax = ?,
                demographic_no = ?, p_name = ?, p_address1 = ?, p_address2 = ?, p_phone = ?, p_birthdate = ?, p_healthcard = ?,
                comments = ?, consultTime = ?
                WHERE ID = ?
                <sql:param value="${current_provider_no}" /> <sql:param value="${current_doc_name}" /> <sql:param value="${current_cl_name}" /> <sql:param value="${current_cl_address1}" /> <sql:param value="${current_cl_address2}" /> <sql:param value="${current_cl_phone}" /> <sql:param value="${current_cl_fax}" />
                <sql:param value="${current_demographic_no}" /> <sql:param value="${current_p_name}" /> <sql:param value="${current_p_address1}" /> <sql:param value="${current_p_address2}" /> <sql:param value="${current_p_phone}" /> <sql:param value="${current_p_birthdate}" /> <sql:param value="${current_p_healthcard}" />
                <sql:param value="${current_comments}" /> <sql:param value="${current_consultTime}" />
                <sql:param value="${current_formId_for_update}" />
            </sql:update>
            <c:if test="${updateCount > 0}"> <% saveSuccess = true; %> </c:if>
<%
        }
        recordJustSaved = true; // Indicates an attempt was made

        if ("exit".equals(submitButtonValue) && saveSuccess && "Save and Exit".equals(request.getParameter("saveexitbut"))) {
%>
            <script type="text/javascript">window.close();</script>
<%
            return;
        }
    }

    if (formId > 0) {
        pageContext.setAttribute("queryFormId", formId);
%>
        <sql:query dataSource="${oscarDB}" var="formDataRow">
            SELECT * FROM formDietician WHERE ID = ?
            <sql:param value="${queryFormId}" />
        </sql:query>
<c:forEach var="row" items="${formDataRow.rows}">
    <%
        java.util.Map actualRow = (java.util.Map) pageContext.getAttribute("row");
        if (actualRow != null) {
            Object val; // Reusable variable for holding retrieved values

            val = actualRow.get("doc_name");
            props.setProperty("doc_name", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_name");
            props.setProperty("cl_name", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_address1");
            props.setProperty("cl_address1", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_address2");
            props.setProperty("cl_address2", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_phone");
            props.setProperty("cl_phone", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_fax");
            props.setProperty("cl_fax", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_name");
            props.setProperty("p_name", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_address1");
            props.setProperty("p_address1", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_address2");
            props.setProperty("p_address2", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_phone");
            props.setProperty("p_phone", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_birthdate");
            props.setProperty("p_birthdate", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_healthcard");
            props.setProperty("p_healthcard", val != null ? String.valueOf(val) : "");
            val = actualRow.get("comments");
            props.setProperty("comments", val != null ? String.valueOf(val) : "");
            
            // Ensure SimpleDateFormat objects are defined (they were inside the loop as per original file)
            SimpleDateFormat dbDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat formDateFormat = new SimpleDateFormat("yyyy/MM/dd");
            
            Object consultTimeObj = actualRow.get("consultTime");
            if (consultTimeObj instanceof java.sql.Date) {
                 props.setProperty("consultTime", formDateFormat.format((java.sql.Date)consultTimeObj));
            } else if (consultTimeObj != null) {
                try { 
                    props.setProperty("consultTime", formDateFormat.format(dbDateFormat.parse(consultTimeObj.toString()))); 
                } catch (java.text.ParseException e) { 
                    // If parsing fails, use the string representation or log error
                    props.setProperty("consultTime", consultTimeObj.toString());
                }
            } else { 
                props.setProperty("consultTime", "");
            }

            Object formCreatedObj = actualRow.get("formCreated");
             if (formCreatedObj instanceof java.sql.Date) {
                 props.setProperty("formCreated", formDateFormat.format((java.sql.Date)formCreatedObj));
            } else if (formCreatedObj != null) {
                try { 
                    props.setProperty("formCreated", formDateFormat.format(dbDateFormat.parse(formCreatedObj.toString()))); 
                } catch (java.text.ParseException e) { 
                    props.setProperty("formCreated", formCreatedObj.toString());
                }
            } else { 
                props.setProperty("formCreated", "");
            }
        } // end if (actualRow != null)
    %>
</c:forEach>

<%
    } else { // New form
        FrmRecord recHelper = (new FrmRecordFactory()).factory("Consultant"); // Use for initial data
        props = recHelper.getFormRecord(LoggedInInfo.getLoggedInInfoFromSession(request), demoNo, 0); // This should load basic patient data if available for demoNo

        FrmConsultantRecord rec1Helper = new FrmConsultantRecord();
        String doctor_name_from_helper = rec1Helper.getProvName(provNo);
        props.setProperty("doc_name", doctor_name_from_helper);

        if (props.getProperty("consultTime", "").isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            props.setProperty("consultTime", sdf.format(new java.util.Date()));
        }
        // Add current date to comments if it's a new form and comments are empty
        if (props.getProperty("comments", "").isEmpty()) {
            SimpleDateFormat sdfForComments = new SimpleDateFormat("yyyy-MM-dd"); // Use yyyy-MM-dd format
            props.setProperty("comments", sdfForComments.format(new java.util.Date()) + "\n");
        }
    }

    props.setProperty("formId", String.valueOf(formId));
    props.setProperty("provider_no", String.valueOf(provNo));
    props.setProperty("demographic_no", String.valueOf(demoNo));
    props.setProperty("form_class", formClass);
    props.setProperty("form_link", formLink);
    props.setProperty("project_home", project_home);

    if (recordJustSaved) {
        if (saveSuccess) {
%>
    <script type="text/javascript">alert("Record saved successfully.");</script>
<%
        } else {
%>
    <script type="text/javascript">alert("Failed to save record. Please check inputs.");</script>
<%
        }
    }
%>
<% // --- END OF SERVER-SIDE LOGIC --- %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Dietician Record</title>
    <link rel="stylesheet" type="text/css" media="all" href="../share/calendar/calendar.css" title="win2k-cold-1" />
    <script type="text/javascript" src="../share/calendar/calendar.js"></script>
    <%-- Assuming struts-bean.tld is available for this: --%>
    <%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
    <script type="text/javascript" src="../share/calendar/lang/<bean:message key="global.javascript.calendar"/>"></script>
    <script type="text/javascript" src="../share/calendar/calendar-setup.js"></script>
</head>
<body onload="cleanForm(); start();">
    <form name="dieticianForm" method="post" action="formDietician.jsp">
        <input type="hidden" name="formAction" value=""/>
        <input type="hidden" name="demographic_no" value="<%= props.getProperty("demographic_no", "0") %>" />
        <input type="hidden" name="formCreated" value="<%= props.getProperty("formCreated", "") %>" />
        <input type="hidden" name="form_class" value="<%= props.getProperty("form_class") %>" />
        <input type="hidden" name="form_link" value="<%= props.getProperty("form_link") %>" />
        <input type="hidden" name="formId" value="<%= props.getProperty("formId", "0") %>" />
        <input type="hidden" name="provider_no" value="<%= props.getProperty("provider_no") %>" />
        <input type="hidden" name="submitAction" value=""/>

        <input type="hidden" name="doc_name" value="<%=props.getProperty("doc_name", "")%>" />
        <input type="hidden" name="cl_name" value="<%=props.getProperty("cl_name","")%>" />
        <input type="hidden" name="cl_address1" value="<%=props.getProperty("cl_address1","")%>" />
        <input type="hidden" name="cl_address2" value="<%=props.getProperty("cl_address2","")%>" />
        <input type="hidden" name="cl_phone" value="<%=props.getProperty("cl_phone","")%>" />
        <input type="hidden" name="cl_fax" value="<%=props.getProperty("cl_fax","")%>" />
        <input type="hidden" name="project_home" value="<%= props.getProperty("project_home") %>" />


        <div style="font-size: 2em; font-family: 'Helvetica Neue', Arial, sans-serif; text-align: center; color: #333; margin-bottom: 10px;">
            <b><%=props.getProperty("doc_name", "")%></b>
        </div>
        <div style="font-size: 1.5em; font-family: 'Helvetica Neue', Arial, sans-serif; text-align: center; color: #555; margin-bottom: 20px;">
            <i><%=props.getProperty("cl_name","")%></i>
        </div>
        <font face="'Helvetica Neue', Arial, sans-serif">
        <TABLE WIDTH="95%" align="center" cellpadding="5" cellspacing="0"  style="font-size: 1em; border-collapse: collapse;">
            <TR>
                <TD style="padding: 5px;"><%=props.getProperty("cl_address1","")%></TD>
                <TD ALIGN="right" style="padding: 5px;">Phone: <%=props.getProperty("cl_phone","")%></TD>
            </TR>
            <TR>
                <TD style="padding: 5px;"><%=props.getProperty("cl_address2","")%></TD>
                <TD ALIGN="right" style="padding: 5px;">Fax: <%=props.getProperty("cl_fax","")%></TD>
            </TR>
        </TABLE>
        <center>
            <TABLE WIDTH="95%" cols="2" style="font-size: 1em; margin-top: 15px;">
                <TR>
                    <INPUT name = "consultTime" id="consultTime" style="border: 1px solid #ccc; font-size: 1em; text-decoration: none; width: 90%; padding: 8px; margin-bottom:10px; border-radius: 4px;" TYPE="text" value="<%=props.getProperty("consultTime", "")%>"/>
                    <TD valign="top">
                    <TABLE WIDTH="100%" align="center" style="border: 1px solid #ddd; border-radius: 5px; padding: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" >
                        <TR>
                            <TD align="left" width="30%" style="padding: 8px; font-weight: bold; color: #444;">Patient:</TD>
                            <TD align="left" style="padding: 8px;">
                                <INPUT NAME="p_name" class="ip" TYPE="text" value="<%=props.getProperty("p_name","")%>"/>
                            </TD>
                        </TR>
                        <TR>
                            <TD align="left" style="padding: 8px; font-weight: bold; color: #444;">Address:</TD>
                            <TD align="left" style="padding: 8px;">
                                <INPUT NAME="p_address1" class="ip" TYPE="text" value="<%=props.getProperty("p_address1","")%>"/>
                            </TD>
                        </TR>
                        <TR>
                            <TD align="left" style="padding: 8px;">&nbsp;</TD>
                            <TD align="left" style="padding: 8px;">
                                <INPUT NAME="p_address2" class="ip" TYPE="text" value="<%=props.getProperty("p_address2","")%>"/>
                            </TD>
                        </TR>
                        <TR>
                            <TD align="left" style="padding: 8px; font-weight: bold; color: #444;">Phone:</TD>
                            <TD align="left" style="padding: 8px;">
                                <INPUT NAME="p_phone" class="ip" TYPE="text" value="<%=props.getProperty("p_phone","")%>"/>
                            </TD>
                        </TR>
                        <TR>
                            <TD align="left" style="padding: 8px; font-weight: bold; color: #444;">Birthdate:</TD>
                            <TD align="left" style="padding: 8px;">
                                <INPUT NAME="p_birthdate" class="ip" TYPE="text" value="<%=props.getProperty("p_birthdate","")%>"/>
                            </TD>
                        </TR>
                        <TR>
                            <TD align="left" style="padding: 8px; font-weight: bold; color: #444;">Health Card No:</TD>
                            <TD align="left" style="padding: 8px;">
                                <INPUT NAME="p_healthcard" class="ip" TYPE="text" value="<%=props.getProperty("p_healthcard","")%>"/>
                            </TD>
                        </TR>
                    </TABLE>
                </TD></TR>
            </TABLE>
        </center>
        <br/>
        <div id="textDiv" style="visibility: hidden; font-size: 1em; font-family: 'Helvetica Neue', Arial, sans-serif; text-align: left; position: absolute; padding: 15px; border: 1px solid #ccc; background-color: #f9f9f9; border-radius: 5px;">
        </div>
        <div id="textareaDiv" style="position: relative; width: 95%; margin: 0 auto;">
            <Strong style="font-size: 1.2em; color: #333; margin-bottom: 8px; display: block;">Record</Strong>
            <textarea id="comments" name="comments" class="ta1" rows="15"><%= props.getProperty("comments", "")%></textarea>
        </div>
        <div id="buttons" style="width: 95%; margin: 20px auto; text-align: center;">
            <input id="anotherTextArea" type="button" value="Add Section" onclick="onAnotherTextArea();" style="padding: 10px 15px; margin-right: 10px; background-color: #5cb85c; color: white; border: none; border-radius: 4px; cursor: pointer;"/>
            <input id="savebut" type="button" value="Save" onclick="onSave();" style="padding: 10px 15px; margin-right: 10px; background-color: #337ab7; color: white; border: none; border-radius: 4px; cursor: pointer;"/>
            <input id="saveexitbut" name="saveexitbut" type="button" value="Save and Exit" onclick="onSaveExit();" style="padding: 10px 15px; margin-right: 10px; background-color: #f0ad4e; color: white; border: none; border-radius: 4px; cursor: pointer;"/>
            <input id="exitbut" type="button" value="Exit" onclick="onExit();" style="padding: 10px 15px; margin-right: 10px; background-color: #d9534f; color: white; border: none; border-radius: 4px; cursor: pointer;"/>
            <input id="printbut" type="button" value="Print" onclick="onPrint();" style="padding: 10px 15px; background-color: #777; color: white; border: none; border-radius: 4px; cursor: pointer;"/>
        </div>
    </form>

    <style type="text/css">
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .tb1{ /* Not currently used, but retained for now */
            border: 1px solid #ddd; 
            width: 100%; 
        }
        .tb2{ /* Not currently used, but retained for now */
            width: 100%; 
            border: 1px solid #ddd; 
        }
        .ip{ /* Input Patient fields */
            border: 1px solid #ccc; 
            font-size: 1em; 
            text-decoration: none; 
            font-family: 'Helvetica Neue', Arial, sans-serif; 
            width: 100%; 
            padding: 8px;
            box-sizing: border-box; /* Ensures padding doesn't add to width */
            border-radius: 4px;
        }
        .ip:focus {
            border-color: #66afe9;
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
        }
        .ta1{ /* Textarea main and additional */
            width: 100%; 
            box-sizing: border-box;
            font-size: 1em; 
            color: black; 
            border: 1px solid #ccc; 
            font-family: 'Helvetica Neue', Arial, sans-serif; 
            padding: 10px;
            border-radius: 4px;
            line-height: 1.5;
        }
        .ta1:focus {
            border-color: #66afe9;
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
        }
        .ta2{ /* Not currently used, but retained for now */
            width: 100%; 
            font-size: 1em; 
            color: black; 
            border: none; 
            font-family: 'Helvetica Neue', Arial, sans-serif; 
        }
        #buttons input[type="button"] {
            transition: background-color 0.2s ease-in-out;
        }
        #buttons input[type="button"]:hover {
            opacity: 0.85;
        }
        hr {
            border: 0;
            height: 1px;
            background: #ddd;
            margin-top: 15px;
            margin-bottom: 15px;
        }
    </style>
    </font>

<script type="text/javascript">
    window.COMMENT_DELIMITER = "\n----NEW_COMMENT_SECTION----\n";

    function loadComments() {
        var mainCommentsTextArea = document.getElementById("comments");
        if (!mainCommentsTextArea) return;
        var fullContent = mainCommentsTextArea.value;
        var parts = fullContent.split(window.COMMENT_DELIMITER);
        var textareaDiv = document.getElementById("textareaDiv");
        var childrenToRemove = [];
        for (var k = textareaDiv.children.length - 1; k >= 0; k--) { // Iterate backwards for removal
            var child = textareaDiv.children[k];
            if (child.tagName === 'HR' || (child.tagName === 'TEXTAREA' && child.id && child.id.startsWith('additional_comments_'))) {
                textareaDiv.removeChild(child);
            }
        }
        if (typeof onAnotherTextArea.counter !== 'undefined') {
            onAnotherTextArea.counter = 0;
        }
        if (parts.length > 0) {
            mainCommentsTextArea.value = parts[0];
        } else {
            mainCommentsTextArea.value = "";
        }
        if (parts.length > 1) {
            for (var i = 1; i < parts.length; i++) {
                onAnotherTextArea(parts[i]); // Pass content to prefill
            }
        }
        if (typeof cleanForm === "function") {
             setTimeout(cleanForm, 200); // Increased timeout slightly
        }
    }

    function combineCommentsForSave() {
        var mainCommentsTextArea = document.getElementById("comments");
        var combinedComments = mainCommentsTextArea.value;
        var additionalCommentsCounter = (typeof onAnotherTextArea.counter !== 'undefined') ? onAnotherTextArea.counter : 0;
        for (var i = 1; i <= additionalCommentsCounter; i++) {
            var additionalTextArea = document.getElementById("additional_comments_" + i);
            if (additionalTextArea) {
                combinedComments += window.COMMENT_DELIMITER + additionalTextArea.value;
            }
        }
        mainCommentsTextArea.value = combinedComments;
    }

    function onPrint() {
        combineCommentsForSave(); 
        var fullTextForPrinting = document.getElementById("comments").value.split(window.COMMENT_DELIMITER).join("\n\n").trim();
        loadComments(); 

        setVisibility('buttons', 'hidden');
        setVisibility('textareaDiv', 'hidden');
        setVisibility('textDiv', 'visible');
        setVisibility('dating', 'hidden');
        var str2 = fullTextForPrinting.replace(/\n/g, "<br />");
        document.getElementById("textDiv").innerHTML = str2;
        setStyle('textDiv', 'position', 'relative'); // Changed from absolute for better flow
        window.print();
        var ret = confirm("Do you wish to make changes?");
        setVisibility('textDiv', 'hidden'); // Always hide textDiv after printing
        setVisibility('textareaDiv', 'visible');
        setVisibility('buttons', 'visible');
        setVisibility('dating', 'visible');
        return ret; 
    }

    function onSave() {
        combineCommentsForSave();
        if (confirm("Are you sure you want to save this form?")) {
            document.dieticianForm.formAction.value="save";
            document.dieticianForm.submit();
        } else {
            loadComments(); 
        }
        return false; 
    }

    function onExit() {
        if(confirm("Are you sure you wish to exit without saving your changes?")) {
            window.close();
        }
        return false;
    }

    function onSaveExit() {
        combineCommentsForSave();
        if (confirm("Are you sure you wish to save and close this window?")) {
            document.dieticianForm.formAction.value="save"; 
            document.dieticianForm.submitAction.value="exit"; // Keep this for server-side detection
            document.dieticianForm.elements['saveexitbut'].value = 'Save and Exit'; // ensure button value is sent if formAction isn't enough
            document.dieticianForm.submit();
        } else {
            loadComments();
        }
        return false;
    }

    function setStyle(obj,style,value){ getRef(obj).style[style]= value; }
    function getRef(obj){ return (typeof obj == "string") ? document.getElementById(obj) : obj; }
    function setClassName(objId, className) { document.getElementById(objId).className = className; }
    function setVisibility(objId, sVisibility) { var obj = document.getElementById(objId); obj.style.visibility = sVisibility; }
    function countLines(strtocount, cols) { var hard_lines = 0; var arr = strtocount.split('\n'); for(var i=0;i<arr.length;i++) { hard_lines += Math.ceil(arr[i].length / cols); } return hard_lines||1; }
    function cleanForm() { 
        var the_form = document.dieticianForm; 
        for ( var x = 0; x < the_form.elements.length; x++ ) { 
            var el = the_form.elements[x];
            if (el.tagName === 'TEXTAREA' && typeof el.rows == "number" ) {
                if (el.id && el.id.startsWith('additional_comments_')) {
                     var lineCount = countLines(el.value, el.cols);
                     // Only adjust if more than 1 line after date, or if default 15 rows isn't enough
                     if (lineCount > 1 && el.value.includes("\n")) {
                         el.rows = lineCount + 1;
                     } else if (lineCount <=1 && el.rows > 15) { // Don't shrink too much if was large
                         el.rows = 15;
                     } else if (lineCount > el.rows) {
                         el.rows = lineCount +1;
                     } else if (el.value.trim() === "" || (el.value.includes("\n") && el.value.substring(el.value.indexOf("\n")+1).trim() === "")){
                         // If only date or empty after date, keep default
                         el.rows = 15;
                     }
                } else if (el.id === 'comments') {
                     el.rows = countLines(el.value,el.cols) + 1;
                }
            }
        } 
        setTimeout(cleanForm, 300); 
    }
    function rs(n,u,w,h,x) { var args="width="+w+",height="+h+",resizable=yes,scrollbars=yes,status=0,top=60,left=30"; var remote=window.open(u,n,args); }

    function start(){
        loadComments();
    }

    function onAnotherTextArea(prefillText) { // Added prefillText parameter
        var textareaDiv = document.getElementById("textareaDiv");
        var separator = document.createElement("hr");
        separator.style.marginTop = "10px";
        separator.style.marginBottom = "10px";
        textareaDiv.appendChild(separator);
        var newTextArea = document.createElement("textarea");
        newTextArea.className = "ta1"; newTextArea.rows = 15;
        
        if (typeof prefillText === 'string' && prefillText.trim() !== '') {
            newTextArea.value = prefillText;
        } else {
            var today = new Date(); var year = today.getFullYear(); var month = ('0' + (today.getMonth() + 1)).slice(-2); var day = ('0' + today.getDate()).slice(-2);
            var formattedDate = year + "-" + month + "-" + day;
            newTextArea.value = formattedDate + "\n";
        }
        newTextArea.placeholder = "Enter additional notes here...";
        if (typeof onAnotherTextArea.counter === 'undefined') { onAnotherTextArea.counter = 0; }
        onAnotherTextArea.counter++;
        var newIdName = "additional_comments_" + onAnotherTextArea.counter;
        newTextArea.name = newIdName; newTextArea.id = newIdName;
        textareaDiv.appendChild(newTextArea);
        if (typeof cleanForm === "function") { // Call cleanForm once after adding new textarea
             setTimeout(cleanForm, 50);
        }
    }
</script>
<script language='javascript'>
   Calendar.setup({inputField:"consultTime",ifFormat:"%Y/%m/%d",showsTime:false,button:"hlSDate",singleClick:true,step:1});
</script>
</body>
</html>
