<%--

    Copyright (c) 2006-. OSCARservice, OpenSoft System. All Rights Reserved.
    This software is published under the GPL GNU General Public License.
    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.

--%>
<%@ taglib uri="/WEB-INF/security.tld" prefix="security"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ page import="oscar.util.*, oscar.form.*, oscar.form.data.*, java.util.Properties, org.oscarehr.util.LoggedInInfo, java.sql.Connection, java.sql.PreparedStatement, java.sql.ResultSet, java.sql.Statement, java.text.SimpleDateFormat" %>

<sql:setDataSource var="oscarDB" dataSource="jdbc/CustomOscarDB" />

<% // --- START OF SERVER-SIDE LOGIC --- %>
<%
    String roleName2$ = (String)session.getAttribute("userrole") + "," + (String) session.getAttribute("user");
    boolean authed=true;
%>
<security:oscarSec roleName="<%=roleName2$%>" objectName="_form" rights="r" reverse="<%=true%>">
	<%authed=false; %>
	<%response.sendRedirect("../securityError.jsp?type=_form");%>
</security:oscarSec>
<%
	if(!authed) {
		return; // Exit if not authorized
	}

    // Initial parameters
    int demoNo = 0;
    String demoNoParam = request.getParameter("demographic_no");
    if (demoNoParam != null && !demoNoParam.isEmpty()) {
        try {
            demoNo = Integer.parseInt(demoNoParam);
        } catch (NumberFormatException e) {
            // Handle error - demoNo is crucial
            out.println("Error: Invalid demographic_no.");
            return;
        }
    }

    String formIdParam = request.getParameter("formId");
    int formId = (formIdParam == null || formIdParam.isEmpty() || "0".equals(formIdParam)) ? 0 : Integer.parseInt(formIdParam);
    
    int provNo = 0;
    String provNoParam = (String) session.getAttribute("user");
     if (provNoParam != null && !provNoParam.isEmpty()) {
        try {
            provNo = Integer.parseInt(provNoParam);
        } catch (NumberFormatException e) {
            out.println("Error: Invalid provider number in session.");
            return;
        }
    }


    String formAction = request.getParameter("formAction");
    String saveExitButtonValue = request.getParameter("saveexitbut");

    Properties props = new Properties();
    String formClass = "DrGaoConsult";
    String formLink = "formDrGaoConsult.jsp";
    String project_home = request.getContextPath().length() > 1 ? request.getContextPath().substring(1) : "";

    boolean recordJustSaved = false;
    boolean saveSuccess = false;

    if ("save".equals(formAction) || "Save and Exit".equals(saveExitButtonValue) ) {
        String p_doc_name = request.getParameter("doc_name");
        String p_cl_name = request.getParameter("cl_name");
        String p_cl_address1 = request.getParameter("cl_address1");
        String p_cl_address2 = request.getParameter("cl_address2");
        String p_cl_phone = request.getParameter("cl_phone");
        String p_cl_fax = request.getParameter("cl_fax");
        String p_consultTime_str = request.getParameter("consultTime");
        String p_comments = request.getParameter("comments");

        String p_p_name = request.getParameter("p_name");
        String p_p_address1 = request.getParameter("p_address1");
        String p_p_address2 = request.getParameter("p_address2");
        String p_p_phone = request.getParameter("p_phone");
        String p_p_birthdate = request.getParameter("p_birthdate");
        String p_p_healthcard = request.getParameter("p_healthcard");

        String sqlConsultTime = null;
        if (p_consultTime_str != null && !p_consultTime_str.isEmpty()) {
            try {
                SimpleDateFormat sdfSource = new SimpleDateFormat("yyyy/MM/dd");
                java.util.Date utilDate = sdfSource.parse(p_consultTime_str);
                SimpleDateFormat sdfDest = new SimpleDateFormat("yyyy-MM-dd");
                sqlConsultTime = sdfDest.format(utilDate);
            } catch (java.text.ParseException e) {
                 out.println("<!-- Error parsing consultTime: " + e.getMessage() + "-->");
                 sqlConsultTime = null; 
            }
        }

        pageContext.setAttribute("current_provider_no", provNo);
        pageContext.setAttribute("current_doc_name", p_doc_name);
        pageContext.setAttribute("current_cl_name", p_cl_name);
        pageContext.setAttribute("current_cl_address1", p_cl_address1);
        pageContext.setAttribute("current_cl_address2", p_cl_address2);
        pageContext.setAttribute("current_cl_phone", p_cl_phone);
        pageContext.setAttribute("current_cl_fax", p_cl_fax);
        pageContext.setAttribute("current_demographic_no", demoNo);
        pageContext.setAttribute("current_p_name", p_p_name);
        pageContext.setAttribute("current_p_address1", p_p_address1);
        pageContext.setAttribute("current_p_address2", p_p_address2);
        pageContext.setAttribute("current_p_phone", p_p_phone);
        pageContext.setAttribute("current_p_birthdate", p_p_birthdate);
        pageContext.setAttribute("current_p_healthcard", p_p_healthcard);
        pageContext.setAttribute("current_comments", p_comments);
        pageContext.setAttribute("current_consultTime", sqlConsultTime); 
        pageContext.setAttribute("current_formId_for_update", formId);


        if (formId == 0) {
%>
            <sql:update dataSource="${oscarDB}" var="insertCount">
                INSERT INTO formDrGaoConsult (provider_no, doc_name, cl_name, cl_address1, cl_address2, cl_phone, cl_fax, demographic_no, p_name, p_address1, p_address2, p_phone, p_birthdate, p_healthcard, comments, consultTime, formCreated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())
                <sql:param value="${current_provider_no}" /> <sql:param value="${current_doc_name}" /> <sql:param value="${current_cl_name}" /> <sql:param value="${current_cl_address1}" /> <sql:param value="${current_cl_address2}" /> <sql:param value="${current_cl_phone}" /> <sql:param value="${current_cl_fax}" />
                <sql:param value="${current_demographic_no}" /> <sql:param value="${current_p_name}" /> <sql:param value="${current_p_address1}" /> <sql:param value="${current_p_address2}" /> <sql:param value="${current_p_phone}" /> <sql:param value="${current_p_birthdate}" /> <sql:param value="${current_p_healthcard}" />
                <sql:param value="${current_comments}" /> <sql:param value="${current_consultTime}" />
            </sql:update>
<%
            saveSuccess = ((Integer)pageContext.getAttribute("insertCount") > 0);
            if (saveSuccess) {
                Connection conn = null; PreparedStatement ps = null; ResultSet rs = null;
                try {
                    javax.sql.DataSource ds = (javax.sql.DataSource) pageContext.findAttribute("oscarDB");
                    conn = ds.getConnection();
                    ps = conn.prepareStatement("SELECT MAX(ID) AS newId FROM formDrGaoConsult WHERE demographic_no = ? AND provider_no = ?");
                    ps.setInt(1, demoNo); ps.setInt(2, provNo);
                    rs = ps.executeQuery();
                    if (rs.next()) { formId = rs.getInt("newId"); }
                } catch (Exception e) { out.println("<!-- Error getting new form ID: " + e.getMessage() + "-->"); e.printStackTrace(new java.io.PrintWriter(out));}
                finally { if (rs != null) rs.close(); if (ps != null) ps.close(); if (conn != null) conn.close(); }
            }
        } else {
%>
            <sql:update dataSource="${oscarDB}" var="updateCount">
                UPDATE formDrGaoConsult SET
                provider_no = ?, doc_name = ?, cl_name = ?, cl_address1 = ?, cl_address2 = ?, cl_phone = ?, cl_fax = ?,
                demographic_no = ?, p_name = ?, p_address1 = ?, p_address2 = ?, p_phone = ?, p_birthdate = ?, p_healthcard = ?,
                comments = ?, consultTime = ?
                WHERE ID = ?
                <sql:param value="${current_provider_no}" /> <sql:param value="${current_doc_name}" /> <sql:param value="${current_cl_name}" /> <sql:param value="${current_cl_address1}" /> <sql:param value="${current_cl_address2}" /> <sql:param value="${current_cl_phone}" /> <sql:param value="${current_cl_fax}" />
                <sql:param value="${current_demographic_no}" /> <sql:param value="${current_p_name}" /> <sql:param value="${current_p_address1}" /> <sql:param value="${current_p_address2}" /> <sql:param value="${current_p_phone}" /> <sql:param value="${current_p_birthdate}" /> <sql:param value="${current_p_healthcard}" />
                <sql:param value="${current_comments}" /> <sql:param value="${current_consultTime}" />
                <sql:param value="${current_formId_for_update}" />
            </sql:update>
            <c:if test="${updateCount > 0}"> <% saveSuccess = true; %> </c:if>
<%
        }
        recordJustSaved = true; 

        if (saveSuccess && "Save and Exit".equals(saveExitButtonValue)) {
%>
            <script type="text/javascript">window.close();</script>
<%
            return;
        }
    }

    if (formId > 0) {
        pageContext.setAttribute("queryFormId", formId);
%>
        <sql:query dataSource="${oscarDB}" var="formDataRow">
            SELECT * FROM formDrGaoConsult WHERE ID = ?
            <sql:param value="${queryFormId}" />
        </sql:query>
<c:forEach var="row" items="${formDataRow.rows}">
    <%
        java.util.Map actualRow = (java.util.Map) pageContext.getAttribute("row");
        if (actualRow != null) {
            Object val; 

            val = actualRow.get("doc_name");
            props.setProperty("doc_name", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_name");
            props.setProperty("cl_name", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_address1");
            props.setProperty("cl_address1", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_address2");
            props.setProperty("cl_address2", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_phone");
            props.setProperty("cl_phone", val != null ? String.valueOf(val) : "");
            val = actualRow.get("cl_fax");
            props.setProperty("cl_fax", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_name");
            props.setProperty("p_name", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_address1");
            props.setProperty("p_address1", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_address2");
            props.setProperty("p_address2", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_phone");
            props.setProperty("p_phone", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_birthdate");
            props.setProperty("p_birthdate", val != null ? String.valueOf(val) : "");
            val = actualRow.get("p_healthcard");
            props.setProperty("p_healthcard", val != null ? String.valueOf(val) : "");
            val = actualRow.get("comments");
            props.setProperty("comments", val != null ? String.valueOf(val) : "");
            
            SimpleDateFormat dbDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat formDateFormat = new SimpleDateFormat("yyyy/MM/dd");
            
            Object consultTimeObj = actualRow.get("consultTime");
            if (consultTimeObj instanceof java.sql.Date) {
                 props.setProperty("consultTime", formDateFormat.format((java.sql.Date)consultTimeObj));
            } else if (consultTimeObj != null) {
                try { 
                    props.setProperty("consultTime", formDateFormat.format(dbDateFormat.parse(consultTimeObj.toString()))); 
                } catch (java.text.ParseException e) { 
                    props.setProperty("consultTime", consultTimeObj.toString());
                }
            } else { 
                props.setProperty("consultTime", "");
            }

            Object formCreatedObj = actualRow.get("formCreated");
             if (formCreatedObj instanceof java.sql.Date) {
                 props.setProperty("formCreated", formDateFormat.format((java.sql.Date)formCreatedObj));
            } else if (formCreatedObj != null) {
                try { 
                    props.setProperty("formCreated", formDateFormat.format(dbDateFormat.parse(formCreatedObj.toString()))); 
                } catch (java.text.ParseException e) { 
                    props.setProperty("formCreated", formCreatedObj.toString());
                }
            } else { 
                props.setProperty("formCreated", "");
            }
        } 
    %>
</c:forEach>

<%
    } else { // New form
        FrmRecord recHelper = (new FrmRecordFactory()).factory("Consultant"); 
        props = recHelper.getFormRecord(LoggedInInfo.getLoggedInInfoFromSession(request), demoNo, 0); 

        FrmConsultantRecord rec1Helper = new FrmConsultantRecord();
        String doctor_name_from_helper = rec1Helper.getProvName(provNo);
        props.setProperty("doc_name", doctor_name_from_helper);

        if (props.getProperty("consultTime", "").isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            props.setProperty("consultTime", sdf.format(new java.util.Date()));
        }
        if (props.getProperty("comments", "").isEmpty()) {
            SimpleDateFormat sdfForComments = new SimpleDateFormat("yyyy-MM-dd"); 
            props.setProperty("comments", sdfForComments.format(new java.util.Date()) + "\\n");
        }
    }

    props.setProperty("formId", String.valueOf(formId));
    props.setProperty("provider_no", String.valueOf(provNo));
    props.setProperty("demographic_no", String.valueOf(demoNo));
    props.setProperty("form_class", formClass);
    props.setProperty("form_link", formLink);
    props.setProperty("project_home", project_home);

    if (recordJustSaved) {
        if (saveSuccess) {
%>
    <script type="text/javascript">alert("Record saved successfully.");</script>
<%
        } else {
%>
    <script type="text/javascript">alert("Failed to save record. Please check inputs.");</script>
<%
        }
    }
%>
<% // --- END OF SERVER-SIDE LOGIC --- %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title><%=props.getProperty("doc_name", "")%> Consultation</title>
    <link rel="stylesheet" type="text/css" media="all" href="../share/calendar/calendar.css" title="win2k-cold-1" />
    <script type="text/javascript" src="../share/calendar/calendar.js"></script>
    <%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
    <script type="text/javascript" src="../share/calendar/lang/<bean:message key="global.javascript.calendar"/>"></script>
    <script type="text/javascript" src="../share/calendar/calendar-setup.js"></script>
    <style type="text/css">
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa; /* Lighter background */
            margin: 0;
            padding: 0; /* Remove body padding, use container padding */
        }
        .form-container {
            width: 90%; /* Max width for larger screens */
            max-width: 960px; /* Limit max width */
            margin: 20px auto; /* Center container */
            padding: 25px;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        .form-header h1 {
            font-size: 2em;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }
        .form-header h2 {
            font-size: 1.5em;
            color: #555;
            margin-bottom: 25px;
            text-align: center;
            font-style: italic;
        }
        .clinic-info-table {
            width: 100%;
            font-size: 1em;
            border-collapse: collapse;
            margin-bottom: 25px;
        }
        .clinic-info-table td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .clinic-info-table td:last-child {
            text-align: right;
        }

        .patient-info-table {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 25px;
        }
        .patient-info-table td {
            padding: 10px 8px;
        }
        .patient-info-table td:first-child {
            font-weight: bold;
            color: #444;
            width: 30%; /* Ensure label column has enough space */
        }
        .ip { /* Input Patient fields and consultTime */
            border: 1px solid #ccc;
            font-size: 1em;
            text-decoration: none;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 4px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        .ip:focus {
            border-color: #007bff; /* Bootstrap primary blue */
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .comments-section strong {
            font-size: 1.2em;
            color: #333;
            margin-bottom: 10px;
            display: block;
        }
        .ta1 { /* Textarea main and additional */
            width: 100%;
            box-sizing: border-box;
            font-size: 1em;
            color: black;
            border: 1px solid #ccc;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            padding: 10px;
            border-radius: 4px;
            line-height: 1.5;
            min-height: 100px; /* Minimum height for textareas */
            resize: vertical; /* Allow vertical resize by user if needed */
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        .ta1:focus {
            border-color: #007bff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .buttons-panel {
            width: 100%; /* Take full width of container */
            margin-top: 25px;
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .buttons-panel input[type="button"] {
            padding: 10px 20px; /* More padding */
            margin: 5px; /* Spacing between buttons */
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease-in-out, opacity 0.2s;
        }
        .buttons-panel input[type="button"]:hover {
            opacity: 0.85;
        }
        #anotherTextArea { background-color: #28a745; } /* Green */
        #savebut { background-color: #007bff; } /* Blue */
        #saveexitbut { background-color: #ffc107; color: #212529; } /* Yellow */
        #exitbut { background-color: #dc3545; } /* Red */
        #printbut { background-color: #6c757d; } /* Gray */

        #textDiv { /* For printing */
            font-size: 1em;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            text-align: left;
            padding: 15px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        hr.comment-separator {
            border: 0;
            height: 1px;
            background: #ddd;
            margin-top: 15px;
            margin-bottom: 15px;
        }

        @media print {
            body * {
                visibility: visible !important;
            }
            #textDiv, #textDiv * {
                visibility: visible !important;
                color: #000 !important;
                background-color: #fff !important;
            }
            #textDiv {
                position: absolute !important;
                left: 0 !important;
                top: 0 !important;
                width: 100% !important;
                padding: 20px !important;
                border: none !important;
                font-size: 12pt !important; /* Ensure readable font size for print */
                line-height: 1.2 !important;
            }
        }
    </style>
</head>
<body onload="start();">
    <div class="form-container">
        <form name="drGaoConsultForm" method="post" action="formDrGaoConsult.jsp">
            <input type="hidden" name="formAction" value=""/>
            <input type="hidden" name="demographic_no" value="<%= props.getProperty("demographic_no", "0") %>" />
            <input type="hidden" name="formCreated" value="<%= props.getProperty("formCreated", "") %>" />
            <input type="hidden" name="form_class" value="<%= props.getProperty("form_class") %>" />
            <input type="hidden" name="form_link" value="<%= props.getProperty("form_link") %>" />
            <input type="hidden" name="formId" value="<%= props.getProperty("formId", "0") %>" />
            <input type="hidden" name="provider_no" value="<%= props.getProperty("provider_no") %>" />
            <%-- No submitAction hidden field needed, we use button name/value --%>

            <input type="hidden" name="doc_name" value="<%=props.getProperty("doc_name", "")%>" />
            <input type="hidden" name="cl_name" value="<%=props.getProperty("cl_name","")%>" />
            <input type="hidden" name="cl_address1" value="<%=props.getProperty("cl_address1","")%>" />
            <input type="hidden" name="cl_address2" value="<%=props.getProperty("cl_address2","")%>" />
            <input type="hidden" name="cl_phone" value="<%=props.getProperty("cl_phone","")%>" />
            <input type="hidden" name="cl_fax" value="<%=props.getProperty("cl_fax","")%>" />
            <input type="hidden" name="project_home" value="<%= props.getProperty("project_home") %>" />

            <div class="form-header">
                <h1><b><%=props.getProperty("doc_name", "")%> Consultation</b></h1>
                <h2><i><%=props.getProperty("cl_name","")%></i></h2>
            </div>

            <table class="clinic-info-table">
                <tr>
                    <td><%=props.getProperty("cl_address1","")%></td>
                    <td>Phone: <%=props.getProperty("cl_phone","")%></td>
                </tr>
                <tr>
                    <td><%=props.getProperty("cl_address2","")%></td>
                    <td>Fax: <%=props.getProperty("cl_fax","")%></td>
                </tr>
            </table>

            <table class="patient-info-table">
                <tr>
                    <td>Consultation Date:</td>
                    <td>
                        <input name="consultTime" id="consultTime" class="ip" type="text" value="<%=props.getProperty("consultTime", "")%>"/>
                    </td>
                </tr>
                <tr>
                    <td>Patient:</td>
                    <td>
                        <input name="p_name" class="ip" type="text" value="<%=props.getProperty("p_name","")%>"/>
                    </td>
                </tr>
                <tr>
                    <td>Address:</td>
                    <td>
                        <input name="p_address1" class="ip" type="text" value="<%=props.getProperty("p_address1","")%>"/>
                    </td>
                </tr>
                <tr>
                    <td>City & Postal Code:</td>
                    <td>
                        <input name="p_address2" class="ip" type="text" value="<%=props.getProperty("p_address2","")%>"/>
                    </td>
                </tr>
                <tr>
                    <td>Phone:</td>
                    <td>
                        <input name="p_phone" class="ip" type="text" value="<%=props.getProperty("p_phone","")%>"/>
                    </td>
                </tr>
                <tr>
                    <td>Birthdate:</td>
                    <td>
                        <input name="p_birthdate" class="ip" type="text" value="<%=props.getProperty("p_birthdate","")%>"/>
                    </td>
                </tr>
                <tr>
                    <td>Health Card No:</td>
                    <td>
                        <input name="p_healthcard" class="ip" type="text" value="<%=props.getProperty("p_healthcard","")%>"/>
                    </td>
                </tr>
            </table>

            <div id="textDiv" style="visibility: hidden;"></div>

            <div id="textareaDiv" class="comments-section">
                <strong>Record</strong>
                <textarea id="comments" name="comments" class="ta1" rows="10"><%= props.getProperty("comments", "")%></textarea>
            </div>

            <div id="buttons" class="buttons-panel">
                <input id="anotherTextArea" type="button" value="Add Section" onclick="onAnotherTextArea();"/>
                <input id="savebut" type="button" value="Save" onclick="onSave();"/>
                <input id="saveexitbut" name="saveexitbut" type="button" value="Save and Exit" onclick="onSaveExit();"/>
                <input id="exitbut" type="button" value="Exit" onclick="onExit();"/>
                <input id="printbut" type="button" value="Print" onclick="onPrint();"/>
            </div>
        </form>
    </div>


<script type="text/javascript">
    window.COMMENT_DELIMITER = "\\n----NEW_COMMENT_SECTION----\\n";

    function autoGrowTextarea(element) {
      element.style.height = "auto"; // Temporarily shrink to get correct scrollHeight
      element.style.height = (element.scrollHeight) + "px";
    }

    function setupAutoGrow() {
        var textareas = document.querySelectorAll('.ta1');
        textareas.forEach(function(textarea) {
            textarea.addEventListener('input', function() {
                autoGrowTextarea(this);
            });
            // Initial resize
            autoGrowTextarea(textarea); 
        });
    }

    function loadComments() {
        var mainCommentsTextArea = document.getElementById("comments");
        if (!mainCommentsTextArea) return;
        var fullContent = mainCommentsTextArea.value;
        var parts = fullContent.split(window.COMMENT_DELIMITER);
        
        var textareaDiv = document.getElementById("textareaDiv");
        var childrenToRemove = [];
        // Iterate backwards for safe removal while iterating
        for (var k = textareaDiv.children.length - 1; k >= 0; k--) { 
            var child = textareaDiv.children[k];
            if (child.tagName === 'HR' || (child.tagName === 'TEXTAREA' && child.id && child.id.startsWith('additional_comments_'))) {
                textareaDiv.removeChild(child);
            }
        }

        if (typeof onAnotherTextArea.counter !== 'undefined') {
            onAnotherTextArea.counter = 0; 
        }

        if (parts.length > 0) {
            mainCommentsTextArea.value = parts[0];
        } else {
            mainCommentsTextArea.value = "";
        }
        
        if (parts.length > 1) {
            for (var i = 1; i < parts.length; i++) {
                onAnotherTextArea(parts[i]); // Pass content to prefill
            }
        }
        // Call autoGrow for all textareas after loading/adding
        setTimeout(setupAutoGrow, 50); // Slight delay to ensure elements are rendered
    }

    function combineCommentsForSave() {
        var mainCommentsTextArea = document.getElementById("comments");
        var combinedComments = mainCommentsTextArea.value;
        var additionalCommentsCounter = (typeof onAnotherTextArea.counter !== 'undefined') ? onAnotherTextArea.counter : 0;
        for (var i = 1; i <= additionalCommentsCounter; i++) {
            var additionalTextArea = document.getElementById("additional_comments_" + i);
            if (additionalTextArea) {
                combinedComments += window.COMMENT_DELIMITER + additionalTextArea.value;
            }
        }
        mainCommentsTextArea.value = combinedComments;
    }

    function onPrint() {
        combineCommentsForSave(); 
        var fullTextForPrinting = document.getElementById("comments").value.split(window.COMMENT_DELIMITER).join("\\n\\n").trim();
        console.log("Text for printing:", fullTextForPrinting);
        loadComments(); // Reload to separate textareas after combining for print

        setVisibility('buttons', 'hidden');
        setVisibility('textareaDiv', 'hidden');
        var textDiv = getRef('textDiv');
        textDiv.innerHTML = fullTextForPrinting.replace(/\\n/g, "<br />");
        setStyle(textDiv, 'position', 'relative'); // Ensure it flows in document
        setVisibility(textDiv, 'visible');
        
        window.print();
        
        var ret = confirm("Do you wish to make changes?");
        setVisibility(textDiv, 'hidden'); 
        setVisibility('textareaDiv', 'visible');
        setVisibility('buttons', 'visible');
        return ret; 
    }

    function onSave() {
        combineCommentsForSave();
        if (confirm("Are you sure you want to save this form?")) {
            document.drGaoConsultForm.formAction.value="save";
            document.drGaoConsultForm.submit();
        } else {
            loadComments(); 
        }
        return false; 
    }

    function onExit() {
        if(confirm("Are you sure you wish to exit without saving your changes?")) {
            window.close();
        }
        return false;
    }

    function onSaveExit() {
        combineCommentsForSave();
        if (confirm("Are you sure you wish to save and close this window?")) {
            document.drGaoConsultForm.formAction.value="save"; // Server still needs this to trigger save logic
            // The button's name ("saveexitbut") and value ("Save and Exit") will be submitted automatically
            document.drGaoConsultForm.submit();
        } else {
            loadComments();
        }
        return false;
    }

    function setStyle(obj,style,value){ getRef(obj).style[style]= value; }
    function getRef(obj){ return (typeof obj == "string") ? document.getElementById(obj) : obj; }
    function setVisibility(objId, sVisibility) { var obj = document.getElementById(objId); if(obj) obj.style.visibility = sVisibility; }
    
    function start(){
        loadComments(); // This will also trigger initial auto-grow via setupAutoGrow
    }

    function onAnotherTextArea(prefillText) { 
        var textareaDiv = document.getElementById("textareaDiv");
        var separator = document.createElement("hr");
        separator.className = "comment-separator";
        textareaDiv.appendChild(separator);
        
        var newTextArea = document.createElement("textarea");
        newTextArea.className = "ta1"; 
        // rows attribute is less critical with auto-grow, but can set an initial small size
        newTextArea.rows = 5; 
        
        if (typeof prefillText === 'string' && prefillText.trim() !== '') {
            newTextArea.value = prefillText;
        } else {
            var today = new Date(); var year = today.getFullYear(); var month = ('0' + (today.getMonth() + 1)).slice(-2); var day = ('0' + today.getDate()).slice(-2);
            var formattedDate = year + "-" + month + "-" + day;
            newTextArea.value = formattedDate + "\n"; // Use double backslash for literal newline in JS string
        }
        newTextArea.placeholder = "Enter additional notes here...";
        
        if (typeof onAnotherTextArea.counter === 'undefined') { onAnotherTextArea.counter = 0; }
        onAnotherTextArea.counter++;
        var newIdName = "additional_comments_" + onAnotherTextArea.counter;
        newTextArea.name = newIdName; newTextArea.id = newIdName;
        
        textareaDiv.appendChild(newTextArea);
        
        // Add event listener for auto-grow to the new textarea
        newTextArea.addEventListener('input', function() { autoGrowTextarea(this); });
        autoGrowTextarea(newTextArea); // Initial resize for the new textarea
    }
</script>
</body>
</html>
