<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    // Set page title for header.jsp - CHANGE THIS FOR EACH PAGE
    request.setAttribute("pageTitle", "Page Title Here"); 
%>

<%@ include file="includes/header.jsp" %>

<div class="container-fluid">
    <div class="row">
        <%@ include file="includes/sidebar.jsp" %>
        
        <!-- 主内容区 -->
        <div class="main-content p-4">
            <!-- 页面特定内容 -->
            <p>This is a template page. Replace content here.</p>
        </div>
    </div>
</div>

<%@ include file="includes/footer.jsp" %>
