<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    // Get demographic_no from request parameter
    String demographicNo = request.getParameter("demographic_no");
    if (demographicNo == null || demographicNo.trim().isEmpty()) {
        response.sendRedirect("hmMenu.jsp");
        return;
    }

    // Set page title for header.jsp
    request.setAttribute("pageTitle", "会员详情");

    pageContext.setAttribute("demographicNo", demographicNo);
%>

<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<!-- Get patient basic information -->
<sql:query dataSource="${snapshot}" var="patientInfo">
    SELECT
        d.demographic_no,
        d.first_name,
        d.last_name,
        DATE_FORMAT(d.date_of_birth, '%Y-%m-%d') AS date_of_birth,
        d.email,
        d.phone,
        d.address,
        d.city,
        d.province,
        d.postal,
        p.provider_no AS main_provider_no,
        CONCAT(p.first_name, ' ', p.last_name) AS main_provider_name
    FROM demographic d
    LEFT JOIN provider p ON d.provider_no = p.provider_no
    WHERE d.demographic_no = ?;
    <sql:param value="${demographicNo}"/>
</sql:query>

<c:if test="${empty patientInfo.rows}">
    <c:redirect url="hmMenu.jsp"/>
</c:if>

<c:set var="patient" value="${patientInfo.rows[0]}"/>

<%@ include file="includes/header.jsp" %>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">查看图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Full Size Image" style="max-width: 100%; max-height: 80vh;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<style>
    .patient-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .nav-tabs .nav-link {
        font-weight: 500;
        color: #495057;
        border: none;
        border-bottom: 3px solid transparent;
        padding: 0.75rem 1rem;
    }
    .nav-tabs .nav-link.active {
        color: #0d6efd;
        border-bottom: 3px solid #0d6efd;
        background-color: transparent;
    }
    .tab-content {
        padding: 1.5rem;
        background-color: #fff;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .patient-info-card {
        background-color: #fff;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    /* Image thumbnail styles */
    .img-thumbnail {
        cursor: pointer;
        transition: transform 0.2s ease-in-out;
    }
    .img-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* Modal image styles */
    #modalImage {
        max-width: 100%;
        max-height: 80vh;
        object-fit: contain;
    }

    /* Package card styles - unified with patient-info-card */
    .package-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        margin-bottom: 1rem;
    }
    .package-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    /* Package status styling with consistent card design */
    .package-status-expired {
        background-color: #fff5f5;
        border-left: 4px solid #dc3545;
    }
    .package-status-active {
        background-color: #f0f9f0;
        border-left: 4px solid #28a745;
    }
    .package-status-warning {
        background-color: #fff8e1;
        border-left: 4px solid #ffc107;
    }
    
    /* Modern package cards */
    .package-full-width-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 12px;
        padding: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        width: 100%;
        border: 1px solid rgba(0,0,0,0.05);
        overflow: hidden;
        position: relative;
    }
    .package-full-width-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    }
    .package-full-width-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--status-gradient);
    }
    
    /* Package card header */
    .package-card-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
        background: rgba(255,255,255,0.7);
        backdrop-filter: blur(10px);
    }
    
    /* Package card body */
    .package-card-body {
        padding: 0 1.5rem 1.5rem 1.5rem;
    }
    
    /* Package stats grid */
    .package-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }
    
    .package-stat-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255,255,255,0.6);
        border-radius: 8px;
        border: 1px solid rgba(0,0,0,0.05);
    }
    
    .package-stat-label {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
    
    .package-stat-value {
        font-size: 1rem;
        font-weight: 600;
        color: #212529;
    }
    
    /* Package summary cards */
    .package-summary-card {
        background: rgba(255,255,255,0.9);
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        text-align: center;
        min-width: 100px;
    }
    
    .package-summary-number {
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1;
    }
    
    .package-summary-label {
        font-size: 0.7rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        opacity: 0.8;
        margin-top: 0.25rem;
    }
    
    /* Compact package cards for legacy support */
    .package-compact-card {
        background-color: #fff;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .package-compact-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    /* Family member card styles */
    .family-member-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border-radius: 12px;
    }
    .family-member-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.12);
    }
    .family-member-card.border-success {
        border-left: 4px solid #28a745 !important;
        background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
    }
    .family-member-card.border-light {
        border-left: 4px solid #dee2e6 !important;
        background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">会员详情</h2>
                <span class="text-muted">当前日期: <%= today %></span>
            </div>

            <!-- 患者基本信息头部 -->
            <div class="patient-header">
                <div class="row">
                    <div class="col-md-8">
                        <h3 class="mb-2">${patient.last_name}, ${patient.first_name}</h3>
                        <p class="mb-1">
                            <span class="badge bg-secondary me-2">档案号: ${patient.demographic_no}</span>
                            <span class="badge bg-info me-2">出生日期: ${patient.date_of_birth}</span>
                        </p>
                        <p class="mb-0 text-muted">
                            <i class="bi bi-envelope me-2"></i>${not empty patient.email ? patient.email : 'N/A'}
                            <i class="bi bi-telephone ms-3 me-2"></i>${not empty patient.phone ? patient.phone : 'N/A'}
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="benefit/addInvoice.jsp?demoid=${patient.demographic_no}" class="btn btn-success me-2">
                            <i class="bi bi-cart-plus"></i> Package Sales
                        </a>
                        <a href="benefit/addRedemptions.jsp?demoid=${patient.demographic_no}" class="btn btn-info me-2">
                            <i class="bi bi-box"></i> Treatment Redemption
                        </a>
                        <button id="exportVaccinesPDF" class="btn btn-primary">
                            <i class="bi bi-file-earmark-pdf"></i> Export Vaccines PDF
                        </button>
                    </div>
                </div>
            </div>

            <!-- 会员基本信息 -->
            <div class="patient-info-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-person-circle me-2 text-primary"></i>会员基本信息
                        </h4>
                        <small class="text-muted">个人资料与联系方式</small>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3 text-primary">
                            <i class="bi bi-person me-2"></i>个人信息
                        </h5>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">姓名:</div>
                            <div class="col-md-8">${patient.last_name}, ${patient.first_name}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">出生日期:</div>
                            <div class="col-md-8">${patient.date_of_birth}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">电子邮箱:</div>
                            <div class="col-md-8">${not empty patient.email ? patient.email : 'N/A'}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">电话:</div>
                            <div class="col-md-8">${not empty patient.phone ? patient.phone : 'N/A'}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="mb-3 text-primary">
                            <i class="bi bi-geo-alt me-2"></i>地址信息
                        </h5>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">地址:</div>
                            <div class="col-md-8">${not empty patient.address ? patient.address : 'N/A'}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">城市:</div>
                            <div class="col-md-8">${not empty patient.city ? patient.city : 'N/A'}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">省份:</div>
                            <div class="col-md-8">${not empty patient.province ? patient.province : 'N/A'}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">邮编:</div>
                            <div class="col-md-8">${not empty patient.postal ? patient.postal : 'N/A'}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-4 fw-bold">主治医生:</div>
                            <div class="col-md-8">${not empty patient.main_provider_name ? patient.main_provider_name : 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 套餐信息区域 -->
            <div class="patient-info-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-box-seam me-2 text-primary"></i>套餐管理
                        </h4>
                        <small class="text-muted">会员套餐购买记录与状态管理</small>
                    </div>
                    <div id="packagesSummary" class="text-end">
                        <!-- Summary will be loaded here -->
                    </div>
                </div>
                <div id="packagesContainer">
                    <div class="text-center text-muted py-4">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        加载套餐信息中...
                    </div>
                </div>
            </div>

            <!-- 预约记录 -->
            <div class="patient-info-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-calendar-event me-2 text-primary"></i>预约记录
                        </h4>
                        <small class="text-muted">历史预约与就诊记录</small>
                    </div>
                </div>
                <div id="appointmentsContainer">
                    <div class="text-center text-muted py-4">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        加载预约信息中...
                    </div>
                </div>
            </div>

            <!-- 疫苗接种记录 -->
            <div class="patient-info-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-shield-plus me-2 text-primary"></i>疫苗接种记录
                        </h4>
                        <small class="text-muted">疫苗接种历史与状态</small>
                    </div>
                </div>
                <div id="vaccinesContainer">
                    <div class="text-center text-muted py-4">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        加载疫苗信息中...
                    </div>
                </div>
            </div>

            <!-- 家庭营养师Billing -->
            <div class="patient-info-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-people me-2 text-primary"></i>家庭营养师Billing
                        </h4>
                        <small class="text-muted">家庭成员营养师服务使用情况</small>
                    </div>
                </div>
                <div id="familyBillingContainer">
                    <div class="text-center text-muted py-4">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        加载家庭营养师信息中...
                    </div>
                </div>
            </div>

            <!-- 治疗记录 -->
            <div class="patient-info-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-bandaid me-2 text-primary"></i>治疗记录
                        </h4>
                        <small class="text-muted">治疗历史与康复记录</small>
                    </div>
                </div>
                <div id="treatmentsContainer">
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-info-circle me-2"></i>治疗记录功能正在开发中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to export vaccines as PDF
    function exportVaccinesPDF() {
        const patient = {
            name: '${patient.last_name}, ${patient.first_name}',
            dob: '${patient.date_of_birth}',
            demographic_no: '${patient.demographic_no}',
            provider: '${patient.main_provider_name}'
        };

        // Create a new jsPDF instance
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set font size and style
        doc.setFontSize(12);

        // Add clinic header with clinic information in the title
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text('Midtown Medical Clinic - Vaccine Records', doc.internal.pageSize.getWidth() / 2, 15, { align: 'center' });
        doc.setFontSize(12);
        doc.text('130-8780 Blundell Road', doc.internal.pageSize.getWidth() / 2, 20, { align: 'center' });
        doc.text('Richmond, British Columbia', doc.internal.pageSize.getWidth() / 2, 25, { align: 'center' });
        doc.text('V6Y 3Y8', doc.internal.pageSize.getWidth() / 2, 30, { align: 'center' });
        doc.text('************', doc.internal.pageSize.getWidth() / 2, 35, { align: 'center' });

        // Add patient information
        doc.setFontSize(12);
        const dobParts = patient.dob.split('-');
        const dobYear = parseInt(dobParts[0]);
        const dobMonth = parseInt(dobParts[1]);
        const dobDay = parseInt(dobParts[2]);
        const today = new Date();

        // Calculate age in years and months for display
        const yearDiff = today.getFullYear() - dobYear;
        const monthDiff = today.getMonth() + 1 - dobMonth;
        const totalMonths = yearDiff * 12 + monthDiff;

        let ageDisplay;
        if (totalMonths < 12) {
            ageDisplay = totalMonths + ' months';
        } else {
            const years = Math.floor(totalMonths / 12);
            const months = totalMonths % 12;
            ageDisplay = years + ' years ' + months + ' months';
        }

                        doc.text('Preventions for ' + patient.name + ' (' + ageDisplay + ') DOB: ' + patient.dob, doc.internal.pageSize.getWidth() / 2, 45, { align: 'center' });
                doc.text('HIN: ' + patient.demographic_no, doc.internal.pageSize.getWidth() / 2, 50, { align: 'center' });
                doc.text('MRP: ' + patient.provider, doc.internal.pageSize.getWidth() / 2, 55, { align: 'center' });

        // Add vaccination table
        doc.setFontSize(11);
        doc.setFont(undefined, 'normal');

        // Get vaccine data
        fetch('getPatientVaccines.jsp?demographic_no=' + '${demographicNo}')
            .then(response => response.json())
            .then(vaccines => {
                if (!vaccines || vaccines.length === 0) {
                    doc.text('No vaccination records found.', 20, 70);
                    doc.save('Vaccines_' + patient.name.replace(/[^a-z0-9]/gi, '_') + '.pdf');
                    return;
                }

                // Process vaccines data - group by prevention_type
                const vaccinesByType = {};
                const processedIds = new Set(); // To track processed vaccine IDs

                vaccines.forEach(vaccine => {
                    const type = vaccine.prevention_type || 'Unknown';
                    const id = vaccine.id;

                    // Skip if we've already processed this vaccine
                    if (processedIds.has(id)) return;
                    processedIds.add(id);

                    if (!vaccinesByType[type]) {
                        vaccinesByType[type] = [];
                    }

                    // Format the doctor name according to requirements
                    // Format: Dr. LastName,FirstName (removing any _xxxxx from FirstName)
                    let doctorName = vaccine.doctor || 'N/A';

                    // Calculate age at vaccination according to BC province rules
                    let ageAtVaccination;
                    const totalMonthDiff = vaccine.totalMonthDiff || 0;

                    if (totalMonthDiff < 12) {
                        // Less than 12 months shows as 'X months'
                        ageAtVaccination = totalMonthDiff + ' months';
                    } else {
                        // 12 months or more shows as 'Y years Z months'
                        const years = Math.floor(totalMonthDiff / 12);
                        const months = totalMonthDiff % 12;
                        ageAtVaccination = years + ' years ' + months + ' months';
                    }

                    // Add the processed vaccine with only the required information
                    vaccinesByType[type].push({
                        id: id,
                        prevention_type: type,
                        prevention_date: vaccine.prevention_date || 'N/A',
                        ageText: ageAtVaccination,
                        doctor: doctorName
                    });
                });

                // Create table
                let yPos = 70;
                const headers = ['Vaccine Type', 'Date', 'Age', 'Provider'];
                const colWidths = [60, 30, 30, 60];

                // Table header
                doc.setFont(undefined, 'bold');
                let xPos = 20;
                headers.forEach((header, i) => {
                    doc.text(header, xPos, yPos);
                    xPos += colWidths[i];
                });

                doc.setFont(undefined, 'normal');
                yPos += 10;

                // Table rows
                Object.keys(vaccinesByType).forEach(type => {
                    const typeVaccines = vaccinesByType[type];
                    // Sort by date (most recent first)
                    typeVaccines.sort((a, b) => new Date(b.prevention_date) - new Date(a.prevention_date));

                    typeVaccines.forEach(vaccine => {
                        // Check if we need a new page
                        if (yPos > 270) {
                            doc.addPage();
                            yPos = 20;

                            // Add header to new page
                            doc.setFont(undefined, 'bold');
                            xPos = 20;
                            headers.forEach((header, i) => {
                                doc.text(header, xPos, yPos);
                                xPos += colWidths[i];
                            });

                            doc.setFont(undefined, 'normal');
                            yPos += 10;
                        }

                        xPos = 20;
                        doc.text(vaccine.prevention_type || 'N/A', xPos, yPos);
                        xPos += colWidths[0];

                        doc.text(vaccine.prevention_date || 'N/A', xPos, yPos);
                        xPos += colWidths[1];

                        doc.text(vaccine.ageText || 'N/A', xPos, yPos);
                        xPos += colWidths[2];

                        doc.text(vaccine.doctor || 'N/A', xPos, yPos);

                        yPos += 7;
                    });
                });

                // Save the PDF with clinic name in the filename
                doc.save('Midtown_Medical_Clinic_Vaccines_' + patient.name.replace(/[^a-z0-9]/gi, '_') + '.pdf');
            })
            .catch(error => {
                console.error('Error generating PDF:', error);
                doc.text('Error generating vaccine report: ' + error.message, 20, 70);
                doc.save('Midtown_Medical_Clinic_Vaccines_' + patient.name.replace(/[^a-z0-9]/gi, '_') + '.pdf');
            });
    }

    // Function to open the image modal
    function openImageModal(imgUrl, imgId) {
        console.log("Opening modal for image:", imgUrl);

        // Set the image source
        const modalImage = document.getElementById('modalImage');
        modalImage.src = imgUrl;

        // Get the modal element
        const modalElement = document.getElementById('imageModal');

        // Initialize and show the modal
        let imageModal;
        try {
            // Try to get existing modal instance
            imageModal = bootstrap.Modal.getInstance(modalElement);
            if (!imageModal) {
                // Create new modal instance if none exists
                imageModal = new bootstrap.Modal(modalElement);
            }
            imageModal.show();
        } catch (error) {
            console.error("Error showing modal:", error);
            // Fallback - open in new tab if modal fails
            window.open(imgUrl, '_blank');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const demographicNo = '${demographicNo}';

        // Add event listener for PDF export button
        document.getElementById('exportVaccinesPDF').addEventListener('click', function() {
            exportVaccinesPDF();
        });

        // Load all content sections immediately since we're not using tabs anymore

        // Load appointments data
        fetch('getPatientAppointments.jsp?demographic_no=' + demographicNo)
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.json();
            })
            .then(appointments => {
                const appointmentsContainer = document.getElementById('appointmentsContainer');
                if (appointments && appointments.length > 0) {
                    let appointmentsHtml = '<div class="list-group">';
                    appointments.forEach(appt => {
                        const reason = appt.reason || 'Appointment';
                        const appointmentDate = appt.appointment_date || 'N/A';
                        const startTime = appt.start_time || 'N/A';
                        const endTime = appt.end_time ? '- ' + appt.end_time : '';
                        const location = appt.location || '';
                        const providerName = appt.provider_name || 'N/A';
                        const notes = appt.notes || '';
                        const images = appt.images || [];

                        let imagesHtml = '';
                        if (images && images.length > 0) {
                            imagesHtml = '<div class="mt-3"><h6>上传的图片:</h6><div class="row">';
                            images.forEach((imgUrl, index) => {
                                if (imgUrl) {
                                    // Create a unique ID for each image
                                    const imgId = 'appointment-img-' + appt.appointment_no + '-' + index;
                                    // Encode the URL to prevent issues with special characters
                                    const encodedImgUrl = imgUrl.replace(/'/g, "\\'");
                                    imagesHtml +=
                                        '<div class="col-md-2 col-sm-4 col-6 mb-3">' +
                                            '<a href="javascript:void(0)" onclick="openImageModal(\'' + encodedImgUrl + '\', \'' + imgId + '\')">' +
                                                '<img id="' + imgId + '"' +
                                                     ' src="' + encodedImgUrl + '"' +
                                                     ' alt="Appointment Image"' +
                                                     ' class="img-thumbnail"' +
                                                     ' style="width: 100%; height: 100px; object-fit: cover;"' +
                                                     ' onerror="this.onerror=null; this.src=\'images/no-image.png\'; this.style.opacity=\'0.5\';">' +
                                            '</a>' +
                                        '</div>';
                                }
                            });
                            imagesHtml += '</div></div>';
                        }

                        appointmentsHtml +=
                            '<div class="list-group-item">' +
                                '<div class="d-flex w-100 justify-content-between">' +
                                    '<h5 class="mb-1">' + reason + '</h5>' +
                                    '<small>' + (appointmentDate ? new Date(appointmentDate).toISOString().split('T')[0] : 'N/A') + '</small>' +
                                '</div>' +
                                '<p class="mb-1">' +
                                    '<i class="bi bi-clock me-2"></i>' + startTime + ' ' + endTime +
                                    (location ? '<i class="bi bi-geo-alt ms-3 me-2"></i>' + location : '') +
                                    '<i class="bi bi-person ms-3 me-2"></i>' + providerName +
                                '</p>' +
                                (notes ? '<div class="mt-2 p-2 bg-light rounded"><small>' + notes.split('\n').join('<br>') + '</small></div>' : '') +
                                imagesHtml +
                            '</div>';
                    });
                    appointmentsHtml += '</div>';
                    appointmentsContainer.innerHTML = appointmentsHtml;
                } else {
                    appointmentsContainer.innerHTML = '<p class="text-center text-muted">没有找到预约记录。</p>';
                }
            })
            .catch(error => {
                console.error('Error fetching appointments:', error);
                document.getElementById('appointmentsContainer').innerHTML =
                    '<p class="text-center text-danger">加载预约信息失败。请查看控制台了解详情。</p>';
            });

        // Load vaccines data
        fetch('getPatientVaccines.jsp?demographic_no=' + demographicNo)
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                        console.log('Raw response:', text);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(vaccines => {
                const vaccinesContainer = document.getElementById('vaccinesContainer');

                if (vaccines && vaccines.length > 0) {
                    // Process vaccines data - group by prevention_type and handle extensions
                    const vaccinesByType = {};
                    const vaccineExtensions = {};

                    // Debug the raw vaccine data
                    console.log("Raw vaccine data:", vaccines);

                    vaccines.forEach(vaccine => {
                        // Make sure we have a valid prevention_type
                        const type = vaccine.prevention_type || 'Unknown';
                        const id = vaccine.id;

                        console.log('Processing vaccine: id=' + id + ', type=' + type);

                        // Handle extensions (keyval/val pairs)
                        if (vaccine.keyval && vaccine.val) {
                            if (!vaccineExtensions[id]) {
                                vaccineExtensions[id] = {};
                            }
                            vaccineExtensions[id][vaccine.keyval] = vaccine.val;
                        }

                        // Only add the vaccine once (not for each extension)
                        if (!vaccinesByType[type]) {
                            vaccinesByType[type] = [];
                        }

                        // Check if we already have this vaccine
                        const existingIndex = vaccinesByType[type].findIndex(v => v.id === id);
                        if (existingIndex === -1) {
                            vaccinesByType[type].push(vaccine);
                        }
                    });

                    // Create a table for each vaccine type
                    let vaccinesHtml = '';

                    // First, create a summary table of all vaccine types
                    vaccinesHtml +=
                        '<div class="card mb-4">' +
                            '<div class="card-header bg-primary text-white">' +
                                '<h5 class="mb-0">Vaccination Summary</h5>' +
                            '</div>' +
                            '<div class="card-body">' +
                                '<div class="table-responsive">' +
                                    '<table class="table table-striped table-bordered">' +
                                        '<thead>' +
                                            '<tr>' +
                                                '<th>Vaccine Type</th>' +
                                                '<th>Count</th>' +
                                                '<th>Latest Date</th>' +
                                                '<th>Age at Vaccination</th>' +
                                                '<th>Provider</th>' +
                                            '</tr>' +
                                        '</thead>' +
                                        '<tbody>';

                    // Log the number of vaccine types
                    console.log("Number of vaccine types:", Object.keys(vaccinesByType).length);

                    Object.keys(vaccinesByType).forEach(type => {
                        const typeVaccines = vaccinesByType[type];
                        // Sort by date (most recent first)
                        typeVaccines.sort((a, b) => new Date(b.prevention_date) - new Date(a.prevention_date));
                        const mostRecent = typeVaccines[0];

                        // Debug the vaccine type
                        console.log("Vaccine type:", type);
                        console.log("Most recent vaccine:", mostRecent);

                        // Log the number of vaccines for this type
                        console.log('Number of vaccines for type ' + type + ': ' + typeVaccines.length);

                        // Create the row with string concatenation for the count
                        vaccinesHtml += '<tr>' +
                            '<td>' + (mostRecent.prevention_type || type || 'N/A') + '</td>' +
                            '<td>' + typeVaccines.length + '</td>' +
                            '<td>' + (mostRecent.prevention_date || 'N/A') + '</td>' +
                            '<td>' + (mostRecent.ageText || 'N/A') + '</td>' +
                            '<td>' + (mostRecent.doctor || 'N/A') + '</td>' +
                            '</tr>';
                    });

                    vaccinesHtml +=
                                        '</tbody>' +
                                    '</table>' +
                                '</div>' +
                            '</div>' +
                        '</div>';

                    // Then, create detailed tables for each vaccine type
                    Object.keys(vaccinesByType).forEach(type => {
                        const typeVaccines = vaccinesByType[type];
                        // Sort by date (most recent first)
                        typeVaccines.sort((a, b) => new Date(b.prevention_date) - new Date(a.prevention_date));

                        // Create the detailed table header with string concatenation
                        vaccinesHtml += '<div class="card mb-4">' +
                            '<div class="card-header">' +
                            '<h5 class="mb-0">Vaccine Type: ' + (type || 'Unknown') + ' (' + typeVaccines.length + ' vaccinations)</h5>' +
                            '</div>' +
                            '<div class="card-body">' +
                            '<div class="table-responsive">' +
                            '<table class="table table-striped table-bordered">' +
                            '<thead>' +
                            '<tr>' +
                            '<th>Vaccine Type</th>' +
                            '<th>Date</th>' +
                            '<th>Age at Vaccination</th>' +
                            '<th>Provider</th>' +
                            '</tr>' +
                            '</thead>' +
                            '<tbody>';

                        typeVaccines.forEach(vaccine => {
                            // Get extension values for this vaccine
                            const extensions = vaccineExtensions[vaccine.id] || {};
                            const location = extensions['location'] || 'N/A';
                            const provider = extensions['provider'] || 'N/A';
                            const dose = extensions['dose'] || 'N/A';
                            const lot = extensions['lot'] || 'N/A';
                            const comments = extensions['comments'] || 'N/A';

                            vaccinesHtml += '<tr>' +
                                '<td>' + (vaccine.prevention_type || 'N/A') + '</td>' +
                                '<td>' + (vaccine.prevention_date || 'N/A') + '</td>' +
                                '<td>' + (vaccine.ageText || 'N/A') + '</td>' +
                                '<td>' + (vaccine.doctor || 'N/A') + '</td>' +
                                '</tr>';
                        });

                        vaccinesHtml += '</tbody>' +
                            '</table>' +
                            '</div>' +
                            '</div>' +
                            '</div>';
                    });

                    vaccinesContainer.innerHTML = vaccinesHtml;
                } else {
                    vaccinesContainer.innerHTML = '<p class="text-center text-muted">没有找到疫苗接种记录。</p>';
                }
            })
            .catch(error => {
                console.error('Error fetching vaccines:', error);
                document.getElementById('vaccinesContainer').innerHTML =
                    '<p class="text-center text-danger">加载疫苗信息失败: ' + error.message + '</p>';
            });

        // Load family nutritionist billing data
        fetch('getFamilyNutritionistBilling.jsp?demographic_no=' + demographicNo)
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.json();
            })
            .then(familyMembers => {
                const familyBillingContainer = document.getElementById('familyBillingContainer');
                if (familyMembers && familyMembers.length > 0) {
                    let familyHtml = '<div class="row">';
                    
                    familyMembers.forEach((member, index) => {
                        const hasNutritionist = member.has_nutritionist_billing === 'YES';
                        const statusClass = hasNutritionist ? 'success' : 'outline-secondary';
                        const statusIcon = hasNutritionist ? 'check-circle-fill' : 'x-circle';
                        const statusText = hasNutritionist ? 'Has AHM_NCN' : 'No AHM_NCN';
                        const cardBorder = hasNutritionist ? 'border-success' : 'border-light';
                        
                        familyHtml += 
                            '<div class="col-md-6 col-lg-4 mb-3">' +
                                '<div class="card h-100 family-member-card ' + cardBorder + '">' +
                                    '<div class="card-body">' +
                                        '<div class="d-flex justify-content-between align-items-start mb-2">' +
                                            '<div>' +
                                                '<h6 class="card-title mb-1 fw-bold">' + (member.full_name || 'Unknown') + '</h6>' +
                                                '<small class="text-muted">' + (member.relationship || 'Unknown relation') + '</small>' +
                                            '</div>' +
                                            '<span class="badge bg-' + statusClass + '">' +
                                                '<i class="bi bi-' + statusIcon + ' me-1"></i>' +
                                            '</span>' +
                                        '</div>' +
                                        '<div class="mt-2">' +
                                            '<small class="' + (hasNutritionist ? 'text-success' : 'text-muted') + '">' +
                                                statusText +
                                            '</small>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>';
                    });
                    
                    familyHtml += '</div>';
                    familyBillingContainer.innerHTML = familyHtml;
                } else {
                    familyBillingContainer.innerHTML = 
                        '<div class="text-center py-5">' +
                            '<div class="mb-4">' +
                                '<i class="bi bi-people display-1 text-muted opacity-50"></i>' +
                            '</div>' +
                            '<h5 class="text-muted mb-2">暂无家庭成员信息</h5>' +
                            '<p class="text-muted">未找到与此会员相关的家庭成员记录</p>' +
                        '</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching family nutritionist billing:', error);
                document.getElementById('familyBillingContainer').innerHTML =
                    '<div class="text-center py-5">' +
                        '<div class="mb-4">' +
                            '<i class="bi bi-exclamation-triangle display-1 text-danger opacity-50"></i>' +
                        '</div>' +
                        '<h5 class="text-danger mb-2">加载失败</h5>' +
                        '<p class="text-muted mb-4">无法加载家庭营养师信息: ' + error.message + '</p>' +
                        '<button class="btn btn-outline-primary" onclick="location.reload()">' +
                            '<i class="bi bi-arrow-clockwise me-2"></i>重新加载' +
                        '</button>' +
                    '</div>';
            });

        // Load treatments data (placeholder for future development)
        document.getElementById('treatmentsContainer').innerHTML =
            '<div class="text-center text-muted py-4">' +
                '<i class="bi bi-construction display-6 mb-3 d-block opacity-50"></i>' +
                '<h5 class="text-muted mb-2">功能开发中</h5>' +
                '<p class="text-muted">治疗记录管理功能即将上线，敬请期待</p>' +
            '</div>';

        // Load packages data
        fetch('getPatientPackages.jsp?demographic_no=' + demographicNo)
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.json();
            })
            .then(packages => {
                const packagesContainer = document.getElementById('packagesContainer');
                if (packages && packages.length > 0) {
                    // Generate summary
                    let activeCount = 0, expiredCount = 0, warningCount = 0, totalValue = 0;
                    
                    packages.forEach(pkg => {
                        totalValue += parseFloat(pkg.bill_amount || 0);
                        if (pkg.status === 'expired') expiredCount++;
                        else if (pkg.days_remaining <= 30) warningCount++;
                        else activeCount++;
                    });
                    
                    // Update summary section
                    document.getElementById('packagesSummary').innerHTML = 
                        '<div class="d-flex gap-2">' +
                            '<div class="package-summary-card text-success">' +
                                '<div class="package-summary-number">' + activeCount + '</div>' +
                                '<div class="package-summary-label">有效</div>' +
                            '</div>' +
                            (warningCount > 0 ? 
                                '<div class="package-summary-card text-warning">' +
                                    '<div class="package-summary-number">' + warningCount + '</div>' +
                                    '<div class="package-summary-label">即将过期</div>' +
                                '</div>' : '') +
                            (expiredCount > 0 ? 
                                '<div class="package-summary-card text-danger">' +
                                    '<div class="package-summary-number">' + expiredCount + '</div>' +
                                    '<div class="package-summary-label">已过期</div>' +
                                '</div>' : '') +
                        '</div>';
                    
                    let packagesHtml = '';
                    
                    packages.forEach((pkg, index) => {
                        const daysRemaining = pkg.days_remaining;
                        let statusClass, statusText, daysText, statusGradient, statusIcon;
                        
                        if (pkg.status === 'expired') {
                            statusClass = 'danger';
                            statusText = '已过期';
                            daysText = '已过期 ' + Math.abs(daysRemaining || 0) + ' 天';
                            statusGradient = 'linear-gradient(135deg, #dc3545, #c82333)';
                            statusIcon = 'x-circle-fill';
                        } else if (pkg.status === 'unknown') {
                            statusClass = 'secondary';
                            statusText = '状态未知';
                            daysText = '日期信息缺失';
                            statusGradient = 'linear-gradient(135deg, #6c757d, #5a6268)';
                            statusIcon = 'question-circle-fill';
                        } else if (daysRemaining <= 30) {
                            statusClass = 'warning';
                            statusText = '即将过期';
                            if (daysRemaining === 0) {
                                daysText = '今天过期';
                            } else {
                                daysText = '剩余 ' + daysRemaining + ' 天';
                            }
                            statusGradient = 'linear-gradient(135deg, #ffc107, #e0a800)';
                            statusIcon = 'exclamation-triangle-fill';
                        } else {
                            statusClass = 'success';
                            statusText = '有效';
                            daysText = '剩余 ' + daysRemaining + ' 天';
                            statusGradient = 'linear-gradient(135deg, #28a745, #1e7e34)';
                            statusIcon = 'check-circle-fill';
                        }
                        
                        packagesHtml += 
                            '<div class="package-full-width-card" style="--status-gradient: ' + statusGradient + '; margin-bottom: ' + (index < packages.length - 1 ? '1.5rem' : '0') + ';">' +
                                '<div class="package-card-header">' +
                                    '<div class="d-flex justify-content-between align-items-start">' +
                                        '<div class="flex-grow-1">' +
                                            '<div class="d-flex align-items-center mb-2">' +
                                                '<i class="bi bi-box2-heart-fill text-primary me-2 fs-5"></i>' +
                                                '<h5 class="mb-0 text-primary fw-bold">' + (pkg.billing_code || 'N/A') + '</h5>' +
                                            '</div>' +
                                            '<h6 class="text-dark mb-0 fw-normal">' + (pkg.description || 'N/A') + '</h6>' +
                                        '</div>' +
                                        '<div class="text-end">' +
                                            '<span class="badge bg-' + statusClass + ' fs-6 px-3 py-2 mb-2">' +
                                                '<i class="bi bi-' + statusIcon + ' me-1"></i>' + statusText +
                                            '</span>' +
                                            '<div class="text-muted small">' + daysText + '</div>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                                '<div class="package-card-body">' +
                                    '<div class="package-stats">' +
                                        '<div class="package-stat-item">' +
                                            '<div class="package-stat-label">套餐金额</div>' +
                                            '<div class="package-stat-value text-success">$' + (pkg.bill_amount || '0.00') + '</div>' +
                                        '</div>' +
                                        '<div class="package-stat-item">' +
                                            '<div class="package-stat-label">购买日期</div>' +
                                            '<div class="package-stat-value">' + (pkg.service_date || 'N/A') + '</div>' +
                                        '</div>' +
                                        '<div class="package-stat-item">' +
                                            '<div class="package-stat-label">到期日期</div>' +
                                            '<div class="package-stat-value">' + (pkg.expiration_date || 'N/A') + '</div>' +
                                        '</div>' +
                                        '<div class="package-stat-item">' +
                                            '<div class="package-stat-label">有效期状态</div>' +
                                            '<div class="package-stat-value text-' + statusClass + '">' +
                                                '<i class="bi bi-' + statusIcon + ' me-1"></i>' + statusText +
                                            '</div>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>';
                    });
                    
                    packagesContainer.innerHTML = packagesHtml;
                } else {
                    // Clear summary
                    document.getElementById('packagesSummary').innerHTML = 
                        '<div class="package-summary-card text-muted">' +
                            '<div class="package-summary-number">0</div>' +
                            '<div class="package-summary-label">套餐</div>' +
                        '</div>';
                    
                    packagesContainer.innerHTML = 
                        '<div class="text-center py-5">' +
                            '<div class="mb-4">' +
                                '<i class="bi bi-box2 display-1 text-muted opacity-50"></i>' +
                            '</div>' +
                            '<h5 class="text-muted mb-2">暂无套餐记录</h5>' +
                            '<p class="text-muted mb-4">此会员尚未购买任何套餐服务</p>' +
                            '<a href="benefit/addInvoice.jsp?demoid=' + demographicNo + '" class="btn btn-primary">' +
                                '<i class="bi bi-plus-circle me-2"></i>购买套餐' +
                            '</a>' +
                        '</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching packages:', error);
                
                // Clear summary on error
                document.getElementById('packagesSummary').innerHTML = 
                    '<div class="package-summary-card text-danger">' +
                        '<div class="package-summary-number">!</div>' +
                        '<div class="package-summary-label">错误</div>' +
                    '</div>';
                
                document.getElementById('packagesContainer').innerHTML =
                    '<div class="text-center py-5">' +
                        '<div class="mb-4">' +
                            '<i class="bi bi-exclamation-triangle display-1 text-danger opacity-50"></i>' +
                        '</div>' +
                        '<h5 class="text-danger mb-2">加载失败</h5>' +
                        '<p class="text-muted mb-4">无法加载套餐信息: ' + error.message + '</p>' +
                        '<button class="btn btn-outline-primary" onclick="location.reload()">' +
                            '<i class="bi bi-arrow-clockwise me-2"></i>重新加载' +
                        '</button>' +
                    '</div>';
            });
    });
</script>

<%@ include file="includes/footer.jsp" %>
