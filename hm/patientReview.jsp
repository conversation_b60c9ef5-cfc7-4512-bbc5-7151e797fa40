<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    // Safely handle any potential parameters that might be parsed as integers
    String demographicNo = request.getParameter("demographic_no");
    int demographicNoInt = 0;
    if (demographicNo != null && !demographicNo.trim().isEmpty()) {
        try {
            demographicNoInt = Integer.parseInt(demographicNo);
        } catch (NumberFormatException e) {
            // Default to 0 if parsing fails
            demographicNoInt = 0;
        }
    }
    request.setAttribute("demographicNoInt", demographicNoInt);
    
    // Use today variable from hmAuth.jsp
    request.setAttribute("today", today);
%>

<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<!-- If a demographic number is provided, fetch patient data -->
<c:if test="${demographicNoInt > 0}">
    <sql:query dataSource="${snapshot}" var="patientResult">
        SELECT * FROM demographic WHERE demographic_no = ?
        <sql:param value="${demographicNoInt}" />
    </sql:query>
    <c:set var="patient" value="${patientResult.rows[0]}" />
</c:if>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Patient Review Form</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background-color: #fff;
            border-bottom: 1px solid #e3e6f0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            margin-bottom: 1.5rem;
            padding: 1rem 0;
        }
        .section-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.10);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            border: 1px solid #e3e6f0;
        }
        .section-title {
            color: #334155;
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 0.75rem;
            margin-bottom: 1.25rem;
            font-weight: 600;
        }
        .form-label {
            font-weight: 500;
            color: #334155;
        }
        .form-control, .form-select {
            border-color: #e3e6f0;
        }
        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
        }
        .btn-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .btn-outline-secondary {
            color: #475569;
            border-color: #e3e6f0;
        }
        .btn-outline-secondary:hover {
            background-color: #f1f5f9;
            color: #334155;
        }
        .table th {
            background-color: #f8fafc;
            color: #334155;
            font-weight: 600;
        }
        .table td, .table th {
            border-color: #e3e6f0;
        }
        @media print {
            .no-print {
                display: none;
            }
            .container-fluid {
                width: 100%;
            }
            .section-card {
                box-shadow: none;
                border: 1px solid #ddd;
                break-inside: avoid;
            }
            body {
                background-color: white !important;
            }
        }
        .actions-bar {
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 0.75rem 0;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
</head>
<body class="bg-light">
<div class="header no-print">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="m-0"><i class="bi bi-clipboard2-pulse text-primary me-2"></i>Patient Review Form</h2>
            <div>
                <a href="https://oscar.mmcwellness.ca/oscar/" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-box-arrow-left"></i> Return to Oscar
                </a>
                <button class="btn btn-outline-secondary me-2" onclick="window.print()">
                    <i class="bi bi-printer"></i> Print Form
                </button>
                <button type="submit" form="patientReviewForm" class="btn btn-primary">
                    <i class="bi bi-save"></i> Save Review
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid pb-5">
    <form id="patientReviewForm" action="savePatientReview.jsp" method="post">
        <!-- Basic Information Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-person text-primary me-2"></i>Basic Information</h4>
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="patientName" class="form-label">Patient Name:</label>
                        <input type="text" class="form-control" id="patientName" name="patientName" value="${patient.first_name} ${patient.last_name}" required>
                        <input type="hidden" name="demographic_no" value="${demographicNoInt}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dateOfBirth" class="form-label">Date of Birth:</label>
                        <input type="date" class="form-control" id="dateOfBirth" name="dateOfBirth" value="${patient.year_of_birth}-${patient.month_of_birth}-${patient.date_of_birth}" required>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reviewDate" class="form-label">Review Date:</label>
                        <input type="date" class="form-control" id="reviewDate" name="reviewDate" value="${today}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reviewedBy" class="form-label">Reviewed By:</label>
                        <input type="text" class="form-control" id="reviewedBy" name="reviewedBy" required>
                    </div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="currentAge" class="form-label">Current Age:</label>
                        <input type="number" class="form-control" id="currentAge" name="currentAge">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="occupation" class="form-label">Occupation:</label>
                        <input type="text" class="form-control" id="occupation" name="occupation">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="height" class="form-label">Height:</label>
                        <input type="text" class="form-control" id="height" name="height">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="weight" class="form-label">Weight:</label>
                        <input type="text" class="form-control" id="weight" name="weight">
                    </div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="memberSince" class="form-label">Member since:</label>
                        <input type="date" class="form-control" id="memberSince" name="memberSince">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="memberExpiry" class="form-label">Member expiry:</label>
                        <input type="date" class="form-control" id="memberExpiry" name="memberExpiry">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Social & Lifestyle History Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-person-walking text-primary me-2"></i>Social & Lifestyle History</h4>
            
            <div class="mb-3">
                <label class="form-label">Smoking:</label>
                <div class="d-flex flex-wrap">
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="smoking" id="smokingNever" value="never">
                        <label class="form-check-label" for="smokingNever">Never</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="smoking" id="smokingFormer" value="former">
                        <label class="form-check-label" for="smokingFormer">Former</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="smoking" id="smokingCurrent" value="current">
                        <label class="form-check-label" for="smokingCurrent">Current</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="smoking" id="smokingVape" value="vape">
                        <label class="form-check-label" for="smokingVape">Vape/E-cigarettes</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="smoking" id="smokingOther" value="other">
                        <label class="form-check-label" for="smokingOther">Other</label>
                    </div>
                </div>
                <div class="mt-2" id="smokingDetailsDiv" style="display: none;">
                    <input type="text" class="form-control" id="smokingDetails" name="smokingDetails" placeholder="Please specify details">
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Alcohol:</label>
                <div class="d-flex flex-wrap">
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="alcohol" id="alcoholNever" value="never">
                        <label class="form-check-label" for="alcoholNever">Never</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="alcohol" id="alcoholOccasional" value="occasional">
                        <label class="form-check-label" for="alcoholOccasional">Occasional (<1/week)</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="alcohol" id="alcoholLight" value="light">
                        <label class="form-check-label" for="alcoholLight">Light (1-2 drinks/week)</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="alcohol" id="alcoholModerate" value="moderate">
                        <label class="form-check-label" for="alcoholModerate">Moderate (3-7 drinks/week)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="alcohol" id="alcoholHeavy" value="heavy">
                        <label class="form-check-label" for="alcoholHeavy">Heavy (>7 drinks/week)</label>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Exercise & Physical Activity:</label>
                <div class="d-flex flex-wrap">
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="exercise" id="exerciseInactive" value="inactive">
                        <label class="form-check-label" for="exerciseInactive">Inactive</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="exercise" id="exerciseLight" value="light">
                        <label class="form-check-label" for="exerciseLight">Light activity</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="exercise" id="exerciseModerate" value="moderate">
                        <label class="form-check-label" for="exerciseModerate">Moderate Activity (2-3x/week)</label>
                    </div>
                    <div class="form-check me-4">
                        <input class="form-check-input" type="radio" name="exercise" id="exerciseActive" value="active">
                        <label class="form-check-label" for="exerciseActive">Active (4-5x/week)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="exercise" id="exerciseVeryActive" value="very-active">
                        <label class="form-check-label" for="exerciseVeryActive">Very active (daily)</label>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Stress & Mental Health Assessment:</label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="phq9" name="phq9" value="yes">
                            <label class="form-check-label" for="phq9">PHQ9 (Depression screening)</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="gad7" name="gad7" value="yes">
                            <label class="form-check-label" for="gad7">GAD-7 (Anxiety screening)</label>
                        </div>
                    </div>
                </div>
                <div class="mt-2">
                    <label for="mentalHealthOther" class="form-label">Other:</label>
                    <input type="text" class="form-control" id="mentalHealthOther" name="mentalHealthOther">
                </div>
            </div>
        </div>
        
        <!-- Vaccination Status Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-shield-check text-primary me-2"></i>Vaccination Status</h4>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="shingleVaccine" name="shingleVaccine">
                            <label class="form-check-label" for="shingleVaccine">Shingle (50+)</label>
                        </div>
                        <input type="date" class="form-control mt-2" id="shingleDate" name="shingleDate">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="pneumococcalVaccine" name="pneumococcalVaccine">
                            <label class="form-check-label" for="pneumococcalVaccine">Pneumococcal Vaccine (50+)</label>
                        </div>
                        <input type="date" class="form-control mt-2" id="pneumococcalDate" name="pneumococcalDate">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="hpvVaccine" name="hpvVaccine">
                            <label class="form-check-label" for="hpvVaccine">HPV</label>
                        </div>
                        <input type="date" class="form-control mt-2" id="hpvDate" name="hpvDate">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="tetanusVaccine" name="tetanusVaccine">
                            <label class="form-check-label" for="tetanusVaccine">Tetanus (every 10 years)</label>
                        </div>
                        <input type="date" class="form-control mt-2" id="tetanusDate" name="tetanusDate">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="menBVaccine" name="menBVaccine">
                            <label class="form-check-label" for="menBVaccine">MenB</label>
                        </div>
                        <input type="date" class="form-control mt-2" id="menBDate" name="menBDate">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="fluVaccine" name="fluVaccine">
                            <label class="form-check-label" for="fluVaccine">Flu shot</label>
                        </div>
                        <input type="date" class="form-control mt-2" id="fluDate" name="fluDate">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="vaccinationNotes" class="form-label">Notes:</label>
                <textarea class="form-control" id="vaccinationNotes" name="vaccinationNotes" rows="3"></textarea>
            </div>
        </div>
        
        <!-- Key Health History Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-clipboard2-pulse text-primary me-2"></i>Key Health History</h4>
            
            <div class="table-responsive mb-3">
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Medical Category</th>
                            <th>Notes</th>
                            <th>Follow-up/To Do</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <select class="form-select" name="acuteCondition">
                                    <option value="">Select Category</option>
                                    <option value="Cardiovascular">Cardiovascular</option>
                                    <option value="Respiratory">Respiratory</option>
                                    <option value="Neurological">Neurological</option>
                                    <option value="Endocrine">Endocrine</option>
                                    <option value="Gastrointestinal">Gastrointestinal</option>
                                    <option value="Genitourinary">Genitourinary</option>
                                    <option value="Mental Health">Mental Health</option>
                                    <option value="Musculoskeletal">Musculoskeletal</option>
                                    <option value="Dermatology">Dermatology</option>
                                    <option value="Hematology">Hematology</option>
                                    <option value="Oncology">Oncology</option>
                                    <option value="Rheumatology">Rheumatology</option>
                                </select>
                            </td>
                            <td>
                                <textarea class="form-control" name="acuteConditionNotes" rows="2"></textarea>
                            </td>
                            <td>
                                <textarea class="form-control" name="acuteConditionFollowup" rows="2"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select class="form-select" name="chronicCondition">
                                    <option value="">Select Category</option>
                                    <option value="Cardiovascular">Cardiovascular</option>
                                    <option value="Respiratory">Respiratory</option>
                                    <option value="Neurological">Neurological</option>
                                    <option value="Endocrine">Endocrine</option>
                                    <option value="Gastrointestinal">Gastrointestinal</option>
                                    <option value="Genitourinary">Genitourinary</option>
                                    <option value="Mental Health">Mental Health</option>
                                    <option value="Musculoskeletal">Musculoskeletal</option>
                                    <option value="Dermatology">Dermatology</option>
                                    <option value="Hematology">Hematology</option>
                                    <option value="Oncology">Oncology</option>
                                    <option value="Rheumatology">Rheumatology</option>
                                </select>
                            </td>
                            <td>
                                <textarea class="form-control" name="chronicConditionNotes" rows="2"></textarea>
                            </td>
                            <td>
                                <textarea class="form-control" name="chronicConditionFollowup" rows="2"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <select class="form-select" name="otherHealthIssue">
                                    <option value="">Select Category</option>
                                    <option value="Cardiovascular">Cardiovascular</option>
                                    <option value="Respiratory">Respiratory</option>
                                    <option value="Neurological">Neurological</option>
                                    <option value="Endocrine">Endocrine</option>
                                    <option value="Gastrointestinal">Gastrointestinal</option>
                                    <option value="Genitourinary">Genitourinary</option>
                                    <option value="Mental Health">Mental Health</option>
                                    <option value="Musculoskeletal">Musculoskeletal</option>
                                    <option value="Dermatology">Dermatology</option>
                                    <option value="Hematology">Hematology</option>
                                    <option value="Oncology">Oncology</option>
                                    <option value="Rheumatology">Rheumatology</option>
                                </select>
                            </td>
                            <td>
                                <textarea class="form-control" name="otherHealthIssueNotes" rows="2"></textarea>
                            </td>
                            <td>
                                <textarea class="form-control" name="otherHealthIssueFollowup" rows="2"></textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="currentMedication" class="form-label">Current Medication:</label>
                    <textarea class="form-control" id="currentMedication" name="currentMedication" rows="3"></textarea>
                </div>
                <div class="col-md-6">
                    <label for="currentSupplements" class="form-label">Current Supplements:</label>
                    <textarea class="form-control" id="currentSupplements" name="currentSupplements" rows="3"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Specialist Involvement Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-person-badge text-primary me-2"></i>Specialist Involvement</h4>
            
            <div class="table-responsive mb-3">
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Specialist</th>
                            <th>Date of Referral</th>
                            <th>Appointment date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="specialist1">
                            </td>
                            <td>
                                <input type="date" class="form-control" name="referralDate1">
                            </td>
                            <td>
                                <input type="date" class="form-control" name="appointmentDate1">
                            </td>
                            <td>
                                <select class="form-select" name="specialistStatus1">
                                    <option value="">Select Status</option>
                                    <option value="Referred">Referred</option>
                                    <option value="Seen">Seen</option>
                                    <option value="Follow-up">Follow-up</option>
                                    <option value="Complete">Complete</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="specialist2">
                            </td>
                            <td>
                                <input type="date" class="form-control" name="referralDate2">
                            </td>
                            <td>
                                <input type="date" class="form-control" name="appointmentDate2">
                            </td>
                            <td>
                                <select class="form-select" name="specialistStatus2">
                                    <option value="">Select Status</option>
                                    <option value="Referred">Referred</option>
                                    <option value="Seen">Seen</option>
                                    <option value="Follow-up">Follow-up</option>
                                    <option value="Complete">Complete</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="specialist3">
                            </td>
                            <td>
                                <input type="date" class="form-control" name="referralDate3">
                            </td>
                            <td>
                                <input type="date" class="form-control" name="appointmentDate3">
                            </td>
                            <td>
                                <select class="form-select" name="specialistStatus3">
                                    <option value="">Select Status</option>
                                    <option value="Referred">Referred</option>
                                    <option value="Seen">Seen</option>
                                    <option value="Follow-up">Follow-up</option>
                                    <option value="Complete">Complete</option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="mb-3">
                <label for="specialistTodo" class="form-label">To-do:</label>
                <textarea class="form-control" id="specialistTodo" name="specialistTodo" rows="3"></textarea>
            </div>
        </div>
        
        <!-- Preventive Screening & Tests Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-clipboard-data text-primary me-2"></i>Preventive Screening & Tests</h4>
            
            <div class="table-responsive mb-3">
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Cancer Screenings</th>
                            <th>Next Due</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="fitTest" name="fitTest">
                                    <label class="form-check-label" for="fitTest">FIT (Fecal Immunochemical Test)</label>
                                </div>
                            </td>
                            <td>
                                <input type="date" class="form-control" name="fitTestDue">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="fitTestNotes">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="mammogram" name="mammogram">
                                    <label class="form-check-label" for="mammogram">Mammogram (≥ 40 years old)</label>
                                </div>
                            </td>
                            <td>
                                <input type="date" class="form-control" name="mammogramDue">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="mammogramNotes">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="papSmear" name="papSmear">
                                    <label class="form-check-label" for="papSmear">Pap Smear (> 25 years old with hx of penetrative sex)</label>
                                </div>
                            </td>
                            <td>
                                <input type="date" class="form-control" name="papSmearDue">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="papSmearNotes">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="bmd" name="bmd">
                                    <label class="form-check-label" for="bmd">Bone Mineral Density (BMD) (≥65 years old)</label>
                                </div>
                            </td>
                            <td>
                                <input type="date" class="form-control" name="bmdDue">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="bmdNotes">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Integrative Care Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-heart-pulse text-primary me-2"></i>Integrative Care</h4>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="womensHealth" name="womensHealth">
                        <label class="form-check-label" for="womensHealth">Women's Health</label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="mensHealth" name="mensHealth">
                        <label class="form-check-label" for="mensHealth">Men's Health</label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="dieticianServices" name="dieticianServices">
                        <label class="form-check-label" for="dieticianServices">Dietician Services</label>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="dexaScan" name="dexaScan">
                        <label class="form-check-label" for="dexaScan">Dexa Scan</label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="functionalMedicine" name="functionalMedicine">
                        <label class="form-check-label" for="functionalMedicine">Functional Medicine and Anti-Aging</label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="peptide" name="peptide">
                        <label class="form-check-label" for="peptide">Peptide</label>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="ivTherapy" name="ivTherapy">
                        <label class="form-check-label" for="ivTherapy">IV Therapy</label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="hrtTrt" name="hrtTrt">
                        <label class="form-check-label" for="hrtTrt">HRT/TRT</label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="medicalWeightLoss" name="medicalWeightLoss">
                        <label class="form-check-label" for="medicalWeightLoss">Medical Weight Loss</label>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="integrativeCareNotes" class="form-label">Notes:</label>
                <textarea class="form-control" id="integrativeCareNotes" name="integrativeCareNotes" rows="3"></textarea>
            </div>
        </div>
        
        <!-- Private Testing & Health Monitoring Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-clipboard2-check text-primary me-2"></i>Private Testing & Health Monitoring</h4>
            
            <div class="table-responsive mb-3">
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Test</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th colspan="3" class="table-light">Whole Body Imaging</th>
                        </tr>
                        <tr>
                            <td>Whole Body MRI</td>
                            <td>
                                <input type="date" class="form-control" name="wholeBodyMriDate">
                            </td>
                            <td>
                                <select class="form-select" name="wholeBodyMriStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Whole Body CT</td>
                            <td>
                                <input type="date" class="form-control" name="wholeBodyCtDate">
                            </td>
                            <td>
                                <select class="form-select" name="wholeBodyCtStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Whole Body Ultrasound</td>
                            <td>
                                <input type="date" class="form-control" name="wholeBodyUltrasoundDate">
                            </td>
                            <td>
                                <select class="form-select" name="wholeBodyUltrasoundStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Abdominal Ultrasound</td>
                            <td>
                                <input type="date" class="form-control" name="abdominalUltrasoundDate">
                            </td>
                            <td>
                                <select class="form-select" name="abdominalUltrasoundStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Carotid Ultrasound</td>
                            <td>
                                <input type="date" class="form-control" name="carotidUltrasoundDate">
                            </td>
                            <td>
                                <select class="form-select" name="carotidUltrasoundStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>CT Colonoscopy</td>
                            <td>
                                <input type="date" class="form-control" name="ctColonoscopyDate">
                            </td>
                            <td>
                                <select class="form-select" name="ctColonoscopyStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>EGD</td>
                            <td>
                                <input type="date" class="form-control" name="egdDate">
                            </td>
                            <td>
                                <select class="form-select" name="egdStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Colonoscopy</td>
                            <td>
                                <input type="date" class="form-control" name="colonoscopyDate">
                            </td>
                            <td>
                                <select class="form-select" name="colonoscopyStatus">
                                    <option value="">Select Status</option>
                                    <option value="booked">Booked</option>
                                    <option value="complete">Complete</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="followup">Follow-up</option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Follow-Up Checklist Section -->
        <div class="section-card">
            <h4 class="section-title"><i class="bi bi-check2-square text-primary me-2"></i>Follow-Up Checklist</h4>
            
            <div class="table-responsive mb-3">
                <table class="table table-bordered" id="followUpTable">
                    <thead class="table-light">
                        <tr>
                            <th>Task</th>
                            <th>Date/Plan</th>
                            <th>Status</th>
                            <th class="text-center" style="width: 50px;">
                                <button type="button" class="btn btn-sm btn-primary" onclick="addFollowUpRow()">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="text" class="form-control" name="followUpTask[]">
                            </td>
                            <td>
                                <input type="text" class="form-control" name="followUpPlan[]">
                            </td>
                            <td>
                                <select class="form-select" name="followUpStatus[]">
                                    <option value="">Select Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="in-progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </td>
                            <td class="text-center">
                                <button type="button" class="btn btn-sm btn-danger" onclick="removeFollowUpRow(this)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Bottom Spacing -->
        <div style="height: 70px;"></div>
    </form>
    
    <!-- Sticky Actions Bar -->
    <div class="actions-bar no-print fixed-bottom bg-white border-top py-3">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">&copy; 2025 Health Management System</small>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="window.print()">
                        <i class="bi bi-printer"></i> Print Form
                    </button>
                    <button type="submit" form="patientReviewForm" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Review
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Calculate age automatically when date of birth changes or when page loads
    function calculateAge() {
        const dobField = document.getElementById('dateOfBirth');
        if (dobField && dobField.value) {
            const dob = new Date(dobField.value);
            const today = new Date();
            let age = today.getFullYear() - dob.getFullYear();
            const monthDiff = today.getMonth() - dob.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
                age--;
            }
            
            document.getElementById('currentAge').value = age;
        }
    }
    
    // Calculate age when date of birth changes
    document.getElementById('dateOfBirth').addEventListener('change', calculateAge);
    
    // Calculate age when page loads
    document.addEventListener('DOMContentLoaded', calculateAge);
    
    // Show smoking details input when "Current" or "Other" is selected
    const smokingRadios = document.querySelectorAll('input[name="smoking"]');
    smokingRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const detailsDiv = document.getElementById('smokingDetailsDiv');
            if (this.value === 'current' || this.value === 'other') {
                detailsDiv.style.display = 'block';
            } else {
                detailsDiv.style.display = 'none';
            }
        });
    });
    
    // Add a new follow-up row
    function addFollowUpRow() {
        const followUpTable = document.getElementById('followUpTable').getElementsByTagName('tbody')[0];
        const newRow = followUpTable.insertRow();
        
        newRow.innerHTML = `
            <td>
                <input type="text" class="form-control" name="followUpTask[]">
            </td>
            <td>
                <input type="text" class="form-control" name="followUpPlan[]">
            </td>
            <td>
                <select class="form-select" name="followUpStatus[]">
                    <option value="">Select Status</option>
                    <option value="pending">Pending</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeFollowUpRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
    }
    
    // Remove a follow-up row
    function removeFollowUpRow(button) {
        const row = button.closest('tr');
        row.parentNode.removeChild(row);
    }
</script>
</body>
</html> 