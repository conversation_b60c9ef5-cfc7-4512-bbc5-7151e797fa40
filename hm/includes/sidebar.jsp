<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<div class="sidebar">
    <div class="p-4 text-white">
        <h3 class="fw-bold mb-4">健康管理系统</h3>
        <div class="mb-4">
            <small class="d-block text-white-50">会员管理</small>
            <hr class="bg-white opacity-25 my-2">
        </div>
        <a href="hmMenu.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/hmMenu.jsp' ? 'active' : ''}">
            <i class="bi bi-search"></i> 会员搜索
        </a>
        <a href="hmDashboard.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/hmDashboard.jsp' ? 'active' : ''}">
            <i class="bi bi-speedometer2"></i> 仪表盘
        </a>
        <a href="members.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/members.jsp' ? 'active' : ''}">
            <i class="bi bi-people-fill"></i> 会员列表
        </a>
        <a href="renewals.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/renewals.jsp' ? 'active' : ''}">
            <i class="bi bi-arrow-repeat"></i> 续费情况
        </a>
        <a href="membersNew.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/membersNew.jsp' ? 'active' : ''}">
            <i class="bi bi-people"></i> 家庭会员
        </a>
        <a href="priexam.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/priexam.jsp' ? 'active' : ''}">
            <i class="bi bi-clipboard-data"></i> 自费项目统计
        </a>

        <!-- 治疗套餐管理系统 -->
        <div class="mb-4 mt-4">
            <small class="d-block text-white-50">治疗套餐管理</small>
            <hr class="bg-white opacity-25 my-2">
        </div>
        <a href="benefit/addTreatment.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addTreatment.jsp' ? 'active' : ''}">
            <i class="bi bi-bandaid"></i> 治疗项目管理
        </a>
        <a href="benefit/addPackage.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addPackage.jsp' ? 'active' : ''}">
            <i class="bi bi-box"></i> 治疗套餐管理
        </a>
        <a href="benefit/addInvoice.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addInvoice.jsp' ? 'active' : ''}">
            <i class="bi bi-cart-plus"></i> 套餐销售
        </a>
        <a href="benefit/addRedemptions.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addRedemptions.jsp' ? 'active' : ''}">
            <i class="bi bi-check-circle"></i> 治疗兑换管理
        </a>
        
        <div class="mb-4 mt-4">
            <small class="d-block text-white-50">系统</small>
            <hr class="bg-white opacity-25 my-2">
        </div>
        <a href="https://oscar.mmcwellness.ca/oscar/provider/providercontrol.jsp" class="sidebar-link">
            <i class="bi bi-box-arrow-left"></i> 返回 Oscar
        </a>
    </div>
</div>
