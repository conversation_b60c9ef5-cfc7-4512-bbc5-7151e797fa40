<%@ page import="java.text.SimpleDateFormat,java.util.*" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // Check if user is logged into Oscar first
    String loggedInUser = (String) session.getAttribute("user");
    if (loggedInUser == null || loggedInUser.trim().isEmpty()) {
        // User not logged into Oscar - redirect to Oscar login
        response.sendRedirect(request.getContextPath() + "/index.jsp");
        return;
    }

    // Password protection for HM module (additional layer)
    String correctPassword = "mmc8780!";
    String enteredPassword = request.getParameter("password");
    String logout = request.getParameter("logout");
    boolean isAuthenticated = false;
    String errorMessage = "";

    // Handle logout
    if (logout != null && logout.equals("true")) {
        session.removeAttribute("hmMenuAuthenticated");
    }

    // Check if password was submitted
    if (enteredPassword != null) {
        if (correctPassword.equals(enteredPassword)) {
            session.setAttribute("hmMenuAuthenticated", "true");
            isAuthenticated = true;
        } else {
            errorMessage = "密码错误，请重试。";
        }
    } else {
        // Check if already authenticated
        String authenticated = (String) session.getAttribute("hmMenuAuthenticated");
        if ("true".equals(authenticated)) {
            isAuthenticated = true;
        }
    }

    // Initialize variables that will be used in authenticated content
    SimpleDateFormat sdf = null;
    String currentMonth = "";
    String today = "";
    String yearStart = "";

    if (isAuthenticated) {
        sdf = new SimpleDateFormat("yyyy-MM-dd");
        currentMonth = new SimpleDateFormat("yyyy-MM").format(new java.util.Date());
        today = sdf.format(new java.util.Date());
        yearStart = new SimpleDateFormat("yyyy").format(new java.util.Date()) + "-01-01";
    }

    // If not authenticated to HM module, show login page
    if (!isAuthenticated) {
        String currentPageName = request.getRequestURI();
        String pageName = currentPageName.substring(currentPageName.lastIndexOf("/") + 1);
        if (pageName.endsWith(".jsp")) {
            pageName = pageName.substring(0, pageName.length() - 4);
        }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问验证 - HM系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 3rem;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .user-info {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <i class="bi bi-shield-lock-fill login-icon"></i>
            <h3 class="fw-bold text-dark">HM系统访问验证</h3>
            <p class="text-muted">请输入密码访问健康管理系统</p>
        </div>
        
        <div class="user-info">
            <small class="text-muted">已登录用户：</small>
            <strong><%= loggedInUser %></strong>
        </div>
        
        <% if (!errorMessage.isEmpty()) { %>
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle-fill"></i> <%= errorMessage %>
            </div>
        <% } %>
        
        <form method="post" action="<%= pageName %>.jsp">
            <div class="mb-4">
                <label for="password" class="form-label">HM系统访问密码</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="请输入HM系统密码" required autofocus>
                </div>
            </div>
            <button type="submit" class="btn btn-primary w-100 py-2">
                <i class="bi bi-unlock-fill"></i> 验证访问
            </button>
        </form>
        
        <div class="text-center mt-3">
            <a href="<%= request.getContextPath() %>/index.jsp" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-arrow-left"></i> 返回Oscar主页
            </a>
        </div>
    </div>
</body>
</html>
<%
        return; // Stop processing the rest of the page
    }
%>