<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.sql.*, java.util.*, java.text.SimpleDateFormat" %>
<%@ page import="javax.servlet.http.*, javax.servlet.*" %>
<%@ page import="java.util.Date" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    request.setAttribute("pageTitle", "自费项目统计");

    String memberLevel = request.getParameter("memberLevel");
    if (memberLevel == null || memberLevel.equals("")) memberLevel = "%";

    String startDate = request.getParameter("startDate");
    if (startDate == null || startDate.equals("")) startDate = "2023-07-01";

    // Use today variable from hmAuth.jsp
    String endDate = request.getParameter("endDate");
    if (endDate == null || endDate.equals("")) endDate = today;

    String sortField = request.getParameter("sortField");
    String sortOrder = request.getParameter("sortOrder");
    if (sortField == null || sortField.isEmpty()) {
        sortField = "bm.service_date";
    }
    if (sortOrder == null || sortOrder.isEmpty()) {
        sortOrder = "ASC";
    }
%>


<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<sql:query dataSource="${snapshot}" var="members">
    SELECT distinct
    CONCAT(bm.demographic_no, '-', dm.last_name, ', ', dm.first_name) AS client,
    CONCAT(bm.billing_code, '-', bs.description) bill,
    bm.bill_amount,
    bm.practitioner_no,
    CONCAT(pr.last_name, ', ', pr.first_name) AS provider,
    DATE_FORMAT(bm.service_date, '%Y-%m-%d') Date
    FROM billingmaster bm
    LEFT JOIN provider pr ON pr.ohip_no = bm.practitioner_no
    INNER JOIN demographic dm ON bm.demographic_no = dm.demographic_no
    INNER JOIN billingservice bs ON bs.service_code = bm.billing_code
    WHERE DATE_FORMAT(bm.service_date, '%Y-%m-%d') >= ?
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') <= ?
    AND bm.billingstatus = 'A'
    AND bm.billing_code NOT LIKE 'AHM%'
    AND bm.billing_code NOT LIKE 'A005'
    AND bm.billing_code NOT LIKE 'AFM%'
    <c:choose>
        <c:when test="${param.memberLevel == 'A[0-9]'}">
            AND bm.billing_code REGEXP '^A[0-9]'
        </c:when>
        <c:otherwise>
            AND bm.billing_code LIKE ?
        </c:otherwise>
    </c:choose>
    ORDER BY <%= sortField %> <%= sortOrder %>;
    <sql:param value="<%= startDate %>" />
    <sql:param value="<%= endDate %>" />
    <c:choose>
        <c:when test="${param.memberLevel != 'A[0-9]'}">
            <sql:param value="<%= memberLevel %>" />
        </c:when>
    </c:choose>
</sql:query>

<sql:query dataSource="${snapshot}" var="monthlyData">
    SELECT DATE_FORMAT(bm.service_date, '%Y-%m') AS month, SUM(bm.bill_amount) AS total_amount
    FROM billingmaster bm
    LEFT JOIN provider pr ON pr.ohip_no = bm.practitioner_no
    INNER JOIN demographic dm ON bm.demographic_no = dm.demographic_no
    INNER JOIN billingservice bs ON bs.service_code = bm.billing_code
    WHERE DATE_FORMAT(bm.service_date, '%Y-%m-%d') >= ?
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') <= ?
    AND bm.billingstatus = 'A'
    AND bm.billing_code NOT LIKE 'AHM%'
    AND bm.billing_code NOT LIKE 'A005'
    AND bm.billing_code NOT LIKE 'AFM%'
    <c:choose>
        <c:when test="${param.memberLevel == 'A[0-9]'}">
            AND bm.billing_code REGEXP '^A[0-9]'
        </c:when>
        <c:otherwise>
            AND bm.billing_code LIKE ?
        </c:otherwise>
    </c:choose>
    GROUP BY DATE_FORMAT(bm.service_date, '%Y-%m')
    ORDER BY month;
    <sql:param value="<%= startDate %>" />
    <sql:param value="<%= endDate %>" />
    <c:choose>
        <c:when test="${param.memberLevel != 'A[0-9]'}">
            <sql:param value="<%= memberLevel %>" />
        </c:when>
    </c:choose>
</sql:query>

<%@ include file="includes/header.jsp" %>

<!-- Body tag is opened in header.jsp, container and row are managed by this page -->
<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <h1 class="text-center my-4">自费项目统计</h1>
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="memberLevel" class="form-label text-danger">选择项目</label>
                    <select name="memberLevel" class="form-select">
                        <option value="%">所有项目</option>
                        <option value="AH%">自费体检项目</option>
                        <option value="A[0-9]">体检外其他项目</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="startDate" class="form-label text-danger">开始时间</label>
                    <input type="date" name="startDate" value="<%= startDate %>" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label text-danger">结束时间</label>
                    <input type="date" name="endDate" value="<%= endDate %>" class="form-control">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-danger w-100">筛选</button>
                </div>
                <input type="hidden" name="sortField" id="sortField" value="<%= sortField %>" />
                <input type="hidden" name="sortOrder" id="sortOrder" value="<%= sortOrder %>" />
            </form>

            <div id="chart-container" class="my-4">
                <canvas id="monthlyChart"></canvas>
            </div>

            <div class="table-responsive">
                <table class="table table-info table-hover my-4">
                    <thead>
                    <tr>
                        <th onclick="updateSortOptions('bm.service_date')" style="cursor:pointer;">Date</th>
                        <th>Client</th>
                        <th>Doctor</th>
                        <th>Item</th>
                        <th>Amount</th>
                    </tr>
                    </thead>
                    <c:set var="total" value="0" />
                    <c:forEach var="member" items="${members.rows}" varStatus="status">
                        <tbody>
                        <tr>
                            <td>${member.Date}</td>
                            <td>${status.index + 1} - ${member.client}</td>
                            <td>${member.provider}</td>
                            <td>${member.bill}</td>
                            <td>${member.bill_amount}</td>
                            <c:set var="total" value="${total + member.bill_amount}" />
                        </tr>
                        </tbody>
                    </c:forEach>
                    <tfoot>
                    <tr>
                        <td colspan="3"></td>
                        <td>Total Amount:</td>
                        <td>${total}</td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    function updateSortOptions(field) {
        var currentOrder = document.getElementById('sortOrder').value;
        var newOrder = (currentOrder === 'ASC') ? 'DESC' : 'ASC';
        document.getElementById('sortField').value = field;
        document.getElementById('sortOrder').value = newOrder;
        document.forms[0].submit();
    }

    window.onload = function() {
        const monthlyData = {
            labels: [
                <c:forEach var="row" items="${monthlyData.rows}" varStatus="status">
                '<c:out value="${row.month}" />'${status.last ? '' : ','}
                </c:forEach>
            ],
            datasets: [{
                label: '总金额',
                data: [
                    <c:forEach var="row" items="${monthlyData.rows}" varStatus="status">
                    <c:out value="${row.total_amount}" />${status.last ? '' : ','}
                    </c:forEach>
                ],
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                fill: true,
            }]
        };

        const config = {
            type: 'line',
            data: monthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '月份'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '总金额'
                        }
                    }
                }
            }
        };

        const ctx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(ctx, config);
    };
</script>
<%@ include file="includes/footer.jsp" %>
