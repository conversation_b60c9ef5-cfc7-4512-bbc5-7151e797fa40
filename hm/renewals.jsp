<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="org.w3c.dom.*, javax.xml.parsers.*" %>
<%@ page import="java.io.*,java.util.*,java.sql.*,java.text.SimpleDateFormat"%>
<%@ page import="javax.servlet.http.*,javax.servlet.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    // Set page title for header.jsp
    request.setAttribute("pageTitle", "会员续费情况");

    // 使用防XSS的方式获取参数
    String memberLevel = request.getParameter("memberLevel");
    if (memberLevel == null || memberLevel.equals("")) memberLevel = "AHM_%";

    String startDate = request.getParameter("startDate");
    if (startDate == null || startDate.equals("")) startDate = "2023";

    // Use today variable from hmAuth.jsp
    String endDate = request.getParameter("endDate");
    if (endDate == null || endDate.equals("")) endDate = today;
    
    // 将处理后的值存储在请求属性中，以便在JSP页面中使用
    request.setAttribute("memberLevel", memberLevel);
    request.setAttribute("startDate", startDate);
    request.setAttribute("endDate", endDate);
%>

<%-- 设置数据源 --%>
<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<%-- 会员续费情况查询 --%>
<sql:query dataSource="${snapshot}" var="renewals">
    SELECT
        b.demographic_no,
        CONCAT(d.last_name, ', ', d.first_name) AS client,
        GROUP_CONCAT(b.billing_code) AS member,
        GROUP_CONCAT(DATE_FORMAT(b.service_date, '%Y-%m-%d')) AS date,
        GROUP_CONCAT(b.bill_amount) AS amount,
        CASE
            WHEN COUNT(b.billing_code) > 1 THEN 'renewed'
            WHEN MAX(b.service_date) < DATE_SUB(CURDATE(), INTERVAL 1 YEAR) THEN 'not renewed'
            ELSE 'new'
        END AS indicator,
        CASE 
            WHEN MAX(b.service_date) < DATE_SUB(CURDATE(), INTERVAL 1 YEAR) THEN 
                DATEDIFF(CURDATE(), DATE_ADD(MAX(b.service_date), INTERVAL 1 YEAR))
            ELSE 
                DATEDIFF(DATE_ADD(MAX(b.service_date), INTERVAL 1 YEAR), CURDATE())
        END AS days_since_last_renewal
    FROM
        billingmaster b
    INNER JOIN
        demographic d ON b.demographic_no = d.demographic_no
    WHERE
        b.billingstatus != 'D'
        AND b.billing_code LIKE ?
        AND DATE_FORMAT(b.service_date, '%Y-%m-%d') >= ?
        AND DATE_FORMAT(b.service_date, '%Y-%m-%d') <= ?
    GROUP BY
        b.demographic_no
    ORDER BY
        indicator DESC, days_since_last_renewal DESC;
    
    <sql:param value="${memberLevel}" />
    <sql:param value="${startDate}" />
    <sql:param value="${endDate}" />
</sql:query>

<%-- 统计数据 --%>
<c:set var="totalMembers" value="0" />
<c:set var="renewedMembers" value="0" />
<c:set var="newMembers" value="0" />
<c:set var="notRenewedMembers" value="0" />

<c:forEach var="member" items="${renewals.rows}">
    <c:set var="totalMembers" value="${totalMembers + 1}" />
    <c:choose>
        <c:when test="${member.indicator == 'renewed'}">
            <c:set var="renewedMembers" value="${renewedMembers + 1}" />
        </c:when>
        <c:when test="${member.indicator == 'new'}">
            <c:set var="newMembers" value="${newMembers + 1}" />
        </c:when>
        <c:otherwise>
            <c:set var="notRenewedMembers" value="${notRenewedMembers + 1}" />
        </c:otherwise>
    </c:choose>
</c:forEach>

<%-- 套餐类型统计 --%>
<sql:query dataSource="${snapshot}" var="member_types">
    SELECT 
        COUNT(CASE WHEN UPPER(billing_code) LIKE 'AHM\\_SIG%' THEN 1 END) AS sig_count,
        COUNT(CASE WHEN UPPER(billing_code) LIKE 'AHM\\_EM%' THEN 1 END) AS em_count,
        COUNT(CASE WHEN UPPER(billing_code) LIKE 'AHM\\_VIP%' THEN 1 END) AS vip_count,
        COUNT(CASE WHEN UPPER(billing_code) NOT LIKE 'AHM\\_SIG%' 
                    AND UPPER(billing_code) NOT LIKE 'AHM\\_EM%' 
                    AND UPPER(billing_code) NOT LIKE 'AHM\\_VIP%' 
                    AND UPPER(billing_code) LIKE 'AHM\\_%' THEN 1 END) AS other_count
    FROM (
        SELECT DISTINCT demographic_no, billing_code
        FROM billingmaster
        WHERE billingstatus != 'D'
        AND UPPER(billing_code) LIKE 'AHM\\_%'
    ) AS distinct_members;
</sql:query>

<%@ include file="includes/header.jsp" %>

<!-- Body tag is opened in header.jsp, container and row are managed by this page -->
<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">健康管理会员续费情况</h2>
                <span class="text-muted">当前日期: <%= today %></span>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-primary">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h5>会员总数</h5>
                        <h3 class="fw-bold">${totalMembers}</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-success">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <h5>已续费会员</h5>
                        <h3 class="fw-bold">${renewedMembers}</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-info">
                            <i class="bi bi-person-plus-fill"></i>
                        </div>
                        <h5>新会员</h5>
                        <h3 class="fw-bold">${newMembers}</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-warning">
                            <i class="bi bi-exclamation-circle-fill"></i>
                        </div>
                        <h5>未续费会员</h5>
                        <h3 class="fw-bold">${notRenewedMembers}</h3>
                    </div>
                </div>
            </div>

            <!-- 图表部分 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">会员续费情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="renewalChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">会员类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="memberTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 续费会员列表 -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">已续费会员列表</h5>
                    <span class="badge bg-success rounded-pill">${renewedMembers} 位会员</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>会员姓名</th>
                                    <th>会员级别</th>
                                    <th>首次加入日期</th>
                                    <th>最近续费日期</th>
                                    <th>续费次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="member" items="${renewals.rows}" varStatus="status">
                                    <c:if test="${member.indicator == 'renewed'}">
                                        <tr>
                                            <th scope="row">${status.index + 1}</th>
                                            <td>
                                                <a href="https://oscar.mmcwellness.ca/oscar/demographic/demographiccontrol.jsp?demographic_no=${fn:escapeXml(member.demographic_no)}&displaymode=edit&dboperation=search_detail" class="text-decoration-none">
                                                    ${fn:escapeXml(member.client)}
                                                </a>
                                            </td>
                                            <td><span class="badge bg-info">${member.member}</span></td>
                                            <td>${member.date}</td>
                                            <td>${member.date}</td>
                                            <td><span class="badge bg-success">${fn:length(fn:split(member.member, ',')) - 1}</span></td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 未续费会员列表 -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">未续费会员列表</h5>
                    <span class="badge bg-warning rounded-pill">${notRenewedMembers} 位会员</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>会员姓名</th>
                                    <th>会员级别</th>
                                    <th>首次加入日期</th>
                                    <th>最近续费日期</th>
                                    <th>距离到期</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="member" items="${renewals.rows}" varStatus="status">
                                    <c:if test="${member.indicator == 'not renewed'}">
                                        <tr>
                                            <th scope="row">${status.index + 1}</th>
                                            <td>
                                                <a href="https://oscar.mmcwellness.ca/oscar/demographic/demographiccontrol.jsp?demographic_no=${fn:escapeXml(member.demographic_no)}&displaymode=edit&dboperation=search_detail" class="text-decoration-none">
                                                    ${fn:escapeXml(member.client)}
                                                </a>
                                            </td>
                                            <td><span class="badge bg-info">${member.member}</span></td>
                                            <td>${member.date}</td>
                                            <td>${member.date}</td>
                                    <td>
                                        <c:set var="daysLeft" value="${member.days_since_last_renewal}" />
                                        <c:choose>
                                            <c:when test="${daysLeft > 0}">
                                                <span class="badge bg-danger">已过期 ${daysLeft} 天</span>
                                            </c:when>
                                            <c:when test="${daysLeft > -30 && daysLeft <= 0}">
                                                <span class="badge bg-warning">即将到期 ${Math.abs(daysLeft)} 天</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge bg-success">还有 ${Math.abs(daysLeft)} 天</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:if>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 续费情况饼图
        const renewalCtx = document.getElementById('renewalChart').getContext('2d');
        const renewalChart = new Chart(renewalCtx, {
            type: 'doughnut',
            data: {
                labels: ['已续费', '新会员', '未续费'],
                datasets: [{
                    data: [parseInt('${renewedMembers}'), parseInt('${newMembers}'), parseInt('${notRenewedMembers}')],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(255, 193, 7, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 会员类型分布饼图
        const memberTypeCtx = document.getElementById('memberTypeChart').getContext('2d');
        const memberTypeChart = new Chart(memberTypeCtx, {
            type: 'pie',
            data: {
                labels: ['SIG', 'EM', 'VIP', '其他'],
                datasets: [{
                    data: [
                        parseInt('${member_types.rows[0].sig_count != null ? member_types.rows[0].sig_count : 0}'),
                        parseInt('${member_types.rows[0].em_count != null ? member_types.rows[0].em_count : 0}'),
                        parseInt('${member_types.rows[0].vip_count != null ? member_types.rows[0].vip_count : 0}'),
                        parseInt('${member_types.rows[0].other_count != null ? member_types.rows[0].other_count : 0}')
                    ],
                    backgroundColor: [
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgba(0, 123, 255, 1)',
                        'rgba(111, 66, 193, 1)',
                        'rgba(220, 53, 69, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });
</script>
<%@ include file="includes/footer.jsp" %>
