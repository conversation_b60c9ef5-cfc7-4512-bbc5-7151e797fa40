<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.util.*, java.text.*, java.sql.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<% 
    String demographicNo = "54897"; // The demographic_no you want to query
    pageContext.setAttribute("demographicNo", demographicNo);
%>

<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Patient Data Viewer (Demographic: <c:out value="${demographicNo}"/>)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .table-responsive { max-height: 400px; margin-bottom: 2rem; }
        h2 { margin-top: 2rem; border-bottom: 1px solid #eee; padding-bottom: 0.5rem; }
        th, td { white-space: nowrap; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">Patient Data for Demographic #<c:out value="${demographicNo}"/></h1>

        <!-- Appointments -->
        <h2>Appointments</h2>
        <sql:query var="appointments" dataSource="${snapshot}">
            SELECT *
            FROM appointment 
            WHERE demographic_no = ?
            ORDER BY appointment_date DESC;
            <sql:param value="${demographicNo}"/>
        </sql:query>
        <c:choose>
            <c:when test="${not empty appointments.rows}">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <c:forEach var="colName" items="${appointments.columnNames}">
                                    <th><c:out value="${colName}"/></th>
                                </c:forEach>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${appointments.rows}">
                                <tr>
                                    <c:forEach var="col" items="${row}">
                                        <td><c:out value="${col}"/></td>
                                    </c:forEach>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:otherwise>
                <p class="text-muted">No appointment records found.</p>
            </c:otherwise>
        </c:choose>

        <!-- Case Management Notes (casemgmt_note) -->
        <h2>Case Management Notes (eChart)</h2>
        <sql:query var="casemgmt_notes" dataSource="${snapshot}">
            SELECT *
            FROM casemgmt_note 
            WHERE demographic_no = ?
            ORDER BY updated_date DESC;
            <sql:param value="${demographicNo}"/>
        </sql:query>
        <c:choose>
            <c:when test="${not empty casemgmt_notes.rows}">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <c:forEach var="colName" items="${casemgmt_notes.columnNames}">
                                    <th><c:out value="${colName}"/></th>
                                </c:forEach>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${casemgmt_notes.rows}">
                                <tr>
                                    <c:forEach var="col" items="${row}">
                                        <td><c:out value="${col}"/></td>
                                    </c:forEach>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:otherwise>
                <p class="text-muted">No case management notes (casemgmt_note) found.</p>
            </c:otherwise>
        </c:choose>

        <!-- Case Management Issues (casemgmt_issue) -->
        <h2>Case Management Issues</h2>
        <sql:query var="casemgmt_issues" dataSource="${snapshot}">
            SELECT *
            FROM casemgmt_issue 
            WHERE demographic_no = ?
            ORDER BY creation_date DESC;
            <sql:param value="${demographicNo}"/>
        </sql:query>
        <c:choose>
            <c:when test="${not empty casemgmt_issues.rows}">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <c:forEach var="colName" items="${casemgmt_issues.columnNames}">
                                    <th><c:out value="${colName}"/></th>
                                </c:forEach>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${casemgmt_issues.rows}">
                                <tr>
                                    <c:forEach var="col" items="${row}">
                                        <td><c:out value="${col}"/></td>
                                    </c:forEach>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:otherwise>
                <p class="text-muted">No case management issues (casemgmt_issue) found.</p>
            </c:otherwise>
        </c:choose>

        <!-- Prescriptions (rxdetail) -->
        <h2>Prescriptions</h2>
        <sql:query var="prescriptions" dataSource="${snapshot}">
            SELECT rxd.*, dr.name AS drug_name, dr.regional_identifier
            FROM rxdetail rxd
            LEFT JOIN drugs dr ON rxd.drugid = dr.drugid
            WHERE rxd.demographic_no = ?
            ORDER BY rxd.dateadded DESC;
            <sql:param value="${demographicNo}"/>
        </sql:query>
        <c:choose>
            <c:when test="${not empty prescriptions.rows}">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <c:forEach var="colName" items="${prescriptions.columnNames}">
                                    <th><c:out value="${colName}"/></th>
                                </c:forEach>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${prescriptions.rows}">
                                <tr>
                                    <c:forEach var="col" items="${row}">
                                        <td><c:out value="${col}"/></td>
                                    </c:forEach>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:otherwise>
                <p class="text-muted">No prescription records (rxdetail) found.</p>
            </c:otherwise>
        </c:choose>

        <!-- Preventions -->
        <h2>Preventions</h2>
        <sql:query var="preventions_data" dataSource="${snapshot}">
            SELECT *
            FROM preventions 
            WHERE demographic_no = ?
            ORDER BY prevention_date DESC;
            <sql:param value="${demographicNo}"/>
        </sql:query>
        <c:choose>
            <c:when test="${not empty preventions_data.rows}">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <c:forEach var="colName" items="${preventions_data.columnNames}">
                                    <th><c:out value="${colName}"/></th>
                                </c:forEach>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${preventions_data.rows}">
                                <tr>
                                    <c:forEach var="col" items="${row}">
                                        <td><c:out value="${col}"/></td>
                                    </c:forEach>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:otherwise>
                <p class="text-muted">No prevention records found.</p>
            </c:otherwise>
        </c:choose>

        <!-- Documents -->
        <h2>Documents</h2>
        <sql:query var="documents_data" dataSource="${snapshot}">
            SELECT *
            FROM documents
            WHERE demographic_no = ? 
            ORDER BY created_date DESC;
            <sql:param value="${demographicNo}"/>
        </sql:query>
        <c:choose>
            <c:when test="${not empty documents_data.rows}">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <c:forEach var="colName" items="${documents_data.columnNames}">
                                    <th><c:out value="${colName}"/></th>
                                </c:forEach>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${documents_data.rows}">
                                <tr>
                                    <c:forEach var="col" items="${row}">
                                        <td><c:out value="${col}"/></td>
                                    </c:forEach>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:when>
            <c:otherwise>
                <p class="text-muted">No document records found.</p>
            </c:otherwise>
        </c:choose>

    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 