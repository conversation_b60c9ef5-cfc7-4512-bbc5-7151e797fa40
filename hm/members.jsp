<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="org.w3c.dom.*, javax.xml.parsers.*" %>
<%@ page import="java.io.*,java.util.*,java.sql.*,java.text.SimpleDateFormat"%>
<%@ page import="javax.servlet.http.*,javax.servlet.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    request.setAttribute("pageTitle", "健康管理会员统计");

    // 使用防XSS的方式获取参数
    String memberLevel = request.getParameter("memberLevel");
    if (memberLevel == null || memberLevel.equals("")) memberLevel = "AHM_%";

    String startDate = request.getParameter("startDate");
    if (startDate == null || startDate.equals("")) startDate = "2023";

    // Use today variable from hmAuth.jsp
    String endDate = request.getParameter("endDate");
    if (endDate == null || endDate.equals("")) endDate = today;

    String sortField = request.getParameter("sortField");
    String sortOrder = request.getParameter("sortOrder");
    
    // 防止SQL注入，进行白名单检查
    String[] allowedSortFields = {"bm.demographic_no", "d.hin", "d.year_of_birth", "bm.service_date", "la.appointment_date"};
    boolean validSortField = false;
    
    if (sortField != null && !sortField.isEmpty()) {
        for (String field : allowedSortFields) {
            if (field.equals(sortField)) {
                validSortField = true;
                break;
            }
        }
    }
    
    if (!validSortField) {
        sortField = "bm.service_date";
    }
    
    if (sortOrder == null || sortOrder.isEmpty() || 
        (!sortOrder.equals("ASC") && !sortOrder.equals("DESC"))) {
        sortOrder = "ASC";
    }
    
    // 将处理后的值存储在请求属性中，以便在JSP页面中使用
    request.setAttribute("sortField", sortField);
    request.setAttribute("sortOrder", sortOrder);
    request.setAttribute("memberLevel", memberLevel);
    request.setAttribute("startDate", startDate);
    request.setAttribute("endDate", endDate);
%>

<%-- 设置数据源，使用常量提高安全性和可维护性 --%>
<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<%-- 主要会员查询，优化SQL语句结构 --%>
<sql:query dataSource="${snapshot}" var="members">
    SELECT DISTINCT 
        bm.demographic_no, 
        CONCAT(d.last_name, ', ', d.first_name) AS Member, 
        bm.billingmaster_no, 
        d.hin, 
        (YEAR(CURDATE()) - d.year_of_birth) AS age,
        bm.billing_code, 
        bm.bill_amount, 
        CONCAT(bm.payee_no, '-', p.last_name, ', ', p.first_name) AS doctor, 
        d.provider_no,
        DATE_FORMAT(bm.service_date, '%Y-%m-%d') AS join_date,
        DATE_FORMAT(la.appointment_date, '%Y-%m-%d') AS last_appointment_date,
        la.reason AS last_reason
    FROM billingmaster bm
    LEFT JOIN demographic d ON d.demographic_no = bm.demographic_no
    LEFT JOIN provider p ON p.provider_no = d.provider_no
    LEFT JOIN (
        SELECT a1.*
        FROM appointment a1
        INNER JOIN (
            SELECT demographic_no, MAX(appointment_date) AS max_date
            FROM appointment
            WHERE status != 'D'
            GROUP BY demographic_no
        ) latest ON a1.demographic_no = latest.demographic_no AND a1.appointment_date = latest.max_date
    ) la ON la.demographic_no = bm.demographic_no
    WHERE bm.billing_code LIKE ?
    AND bm.billingstatus != 'D'
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') >= ?
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') <= ?
    GROUP BY bm.demographic_no
    ORDER BY ${sortField} ${sortOrder};
    
    <sql:param value="${memberLevel}" />
    <sql:param value="${startDate}" />
    <sql:param value="${endDate}" />
</sql:query>

<%-- 修改分组会员查询，统一状态条件 --%>
<sql:query dataSource="${snapshot}" var="groupedMembers">
    SELECT
        b.demographic_no,
        CONCAT(d.last_name, ', ', d.first_name) AS client,
        GROUP_CONCAT(b.billing_code) AS member,
        GROUP_CONCAT(DATE_FORMAT(b.service_date, '%Y-%m-%d')) AS date,
        GROUP_CONCAT(b.bill_amount) AS amount,
        CASE
            WHEN COUNT(b.billing_code) > 1 THEN 'renewed'
            WHEN MAX(b.service_date) < DATE_SUB(CURDATE(), INTERVAL 1 YEAR) THEN 'not renewed'
            ELSE 'new'
        END AS indicator
    FROM
        billingmaster b
    INNER JOIN
        demographic d ON b.demographic_no = d.demographic_no
    WHERE
        b.billingstatus != 'D'
        AND b.billing_code LIKE ?
    GROUP BY
        b.demographic_no
    ORDER BY
        indicator DESC, b.demographic_no ASC;
    
    <sql:param value="${memberLevel}" />
</sql:query>

<%-- 月度数据查询，优化性能 --%>
<sql:query dataSource="${snapshot}" var="monthlyData">
    SELECT DATE_FORMAT(bm.service_date, '%Y-%m') AS month, SUM(bm.bill_amount) AS total_amount
    FROM billingmaster bm
    WHERE bm.billing_code LIKE ?
    AND bm.billingstatus != 'D'
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') >= ?
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') <= ?
    GROUP BY DATE_FORMAT(bm.service_date, '%Y-%m')
    ORDER BY month;
    
    <sql:param value="${memberLevel}" />
    <sql:param value="${startDate}" />
    <sql:param value="${endDate}" />
</sql:query>

<%@ include file="includes/header.jsp" %>

<!-- Body tag is opened in header.jsp, container and row are managed by this page -->
<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">健康管理会员统计</h2>
                <span class="text-muted">当前日期: <%= today %></span>
            </div>

            <form method="get" class="row g-3 mb-4 no-print">
                <div class="col-md-3">
                    <label for="memberLevel" class="form-label text-danger">选择会员级别</label>
                    <select name="memberLevel" id="memberLevel" class="form-select">
                        <option value="AHM_%">Select Member Level</option>
                        <option value="AHM_%">ALL</option>
                        <option value="AHM_%">--------------</option>
                        <option value="AHM_SIG%">All SIGs</option>
                        <option value="AHM_SIG">SIG Only</option>
                        <option value="AHM_SIG_FM">AHM_SIG_FM</option>
                        <option value="AHM_SIG_KD">AHM_SIG_KD</option>
                        <option value="AHM_SIG_YG">AHM_SIG_YG</option>
                        <option value="AHM_%">--------------</option>
                        <option value="AHM_EM%">All EMs</option>
                        <option value="AHM_EM">EM Only</option>
                        <option value="AHM_EM_FM">AHM_EM_FM</option>
                        <option value="AHM_EM_KD">AHM_EM_KD</option>
                        <option value="AHM_EM_YG">AHM_EM_YG</option>
                        <option value="AHM_%">--------------</option>
                        <option value="AHM_VIP%">AHM_VIP</option>
                        <option value="AHM_VIP">VIP Only</option>
                        <option value="AHM_VIP_FM">AHM_VIP_FM</option>
                        <option value="AHM_VIP_KD">AHM_VIP_KD</option>
                        <option value="AHM_VIP_YG">AHM_VIP_YG</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="startDate" class="form-label text-danger">开始时间</label>
                    <input type="date" id="startDate" name="startDate" value="${startDate}" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label text-danger">结束时间</label>
                    <input type="date" id="endDate" name="endDate" value="${endDate}" class="form-control">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-danger w-100">筛选</button>
                </div>
                <input type="hidden" name="sortField" id="sortField" value="${sortField}" />
                <input type="hidden" name="sortOrder" id="sortOrder" value="${sortOrder}" />
            </form>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">月度金额统计</h5>
                </div>
                <div class="card-body">
                    <div id="chart-container" style="height: 300px;">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="row mb-2 no-print">
                <div class="col-12">
                    <button class="btn btn-secondary" onclick="window.print()">
                        <i class="bi bi-printer"></i> 打印报告
                    </button>
                    <button class="btn btn-primary" onclick="exportTableToCSV('会员数据.csv')">
                        <i class="bi bi-download"></i> 导出CSV
                    </button>
                </div>
            </div>
            
            <%-- 主会员表格，优化HTML结构 --%>
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">会员列表</h5>
                    <span class="badge bg-primary">总计: ${members.rowCount} 位会员</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped my-0 table-hover">
                            <thead class="table-light">
                            <tr>
                                <th class="sortable" onclick="updateSortOptions('bm.demographic_no')">
                                    DemoID <c:if test="${sortField == 'bm.demographic_no'}">
                                        <i class="bi bi-${sortOrder == 'ASC' ? 'sort-up' : 'sort-down'}"></i>
                                    </c:if>
                                </th>
                                <th>Client</th>
                                <th class="sortable" onclick="updateSortOptions('d.hin')">
                                    MSP # <c:if test="${sortField == 'd.hin'}">
                                        <i class="bi bi-${sortOrder == 'ASC' ? 'sort-up' : 'sort-down'}"></i>
                                    </c:if>
                                </th>
                                <th class="sortable" onclick="updateSortOptions('d.year_of_birth')">
                                    Age <c:if test="${sortField == 'd.year_of_birth'}">
                                        <i class="bi bi-${sortOrder == 'ASC' ? 'sort-up' : 'sort-down'}"></i>
                                    </c:if>
                                </th>
                                <th>Doctor</th>
                                <th class="sortable" onclick="updateSortOptions('bm.service_date')">
                                    Join Date <c:if test="${sortField == 'bm.service_date'}">
                                        <i class="bi bi-${sortOrder == 'ASC' ? 'sort-up' : 'sort-down'}"></i>
                                    </c:if>
                                </th>
                                <th>Member Level</th>
                                <th>Amount</th>
                                <th class="sortable" onclick="updateSortOptions('la.appointment_date')">
                                    Last Appointment <c:if test="${sortField == 'la.appointment_date'}">
                                        <i class="bi bi-${sortOrder == 'ASC' ? 'sort-up' : 'sort-down'}"></i>
                                    </c:if>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:set var="total" value="0" />
                            <c:set var="msp" value="0" />
                            <c:set var="pri" value="0" />
                            <c:forEach var="member" items="${members.rows}" varStatus="status">
                                <c:choose>
                                    <c:when test="${not empty member.hin}">
                                        <c:set var="pri" value="${pri + 1}" />
                                    </c:when>
                                    <c:otherwise>
                                        <c:set var="msp" value="${msp + 1}" />
                                    </c:otherwise>
                                </c:choose>
                                <tr>
                                    <th scope="row">${status.index+1} - ${member.demographic_no}</th>
                                    <td><a href="https://oscar.mmcwellness.ca/oscar/demographic/demographiccontrol.jsp?demographic_no=${fn:escapeXml(member.demographic_no)}&apptProvider=${fn:escapeXml(member.provider_no)}&displaymode=edit&dboperation=search_detail" class="text-decoration-none">${fn:escapeXml(member.Member)}</a></td>
                                    <td>${fn:escapeXml(member.hin)}</td>
                                    <td>${member.age}</td>
                                    <td>${fn:escapeXml(member.doctor)}</td>
                                    <td>${member.join_date}</td>
                                    <td><span class="badge bg-info">${fn:escapeXml(member.billing_code)}</span></td>
                                    <td>${member.bill_amount}</td>
                                    <c:set var="total" value="${total + member.bill_amount}" />
                                    <td>
                                        <c:if test="${not empty member.last_appointment_date}">
                                            ${member.last_appointment_date} <span class="badge bg-secondary">${fn:escapeXml(member.last_reason)}</span>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                            <tfoot>
                            <tr class="fw-bold bg-light">
                                <td>Pri Count:</td>
                                <td>${msp}</td>
                                <td>MSP Count:</td>
                                <td colspan="3">${pri}</td>
                                <td>Total Amount:</td>
                                <td>${total}</td>
                                <td></td>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 设置当前选择的会员级别
    document.addEventListener('DOMContentLoaded', function() {
        const memberLevelSelect = document.getElementById('memberLevel');
        const currentLevel = '${memberLevel}'.replace('\\', '');
        
        for (let i = 0; i < memberLevelSelect.options.length; i++) {
            if (memberLevelSelect.options[i].value === currentLevel) {
                memberLevelSelect.selectedIndex = i;
                break;
            }
        }
    });

    // 优化排序功能
    function updateSortOptions(field) {
        const currentField = document.getElementById('sortField').value;
        const currentOrder = document.getElementById('sortOrder').value;
        let newOrder = 'ASC';
        
        if (currentField === field) {
            newOrder = (currentOrder === 'ASC') ? 'DESC' : 'ASC';
        }
        
        document.getElementById('sortField').value = field;
        document.getElementById('sortOrder').value = newOrder;
        
        // 使用表单提交
        document.forms[0].submit();
    }

    // 导出CSV功能
    function exportTableToCSV(filename) {
        const table = document.querySelector('table');
        let csv = [];
        let rows = table.querySelectorAll('tr');
        
        for (let i = 0; i < rows.length; i++) {
            let row = [], cols = rows[i].querySelectorAll('td, th');
            
            for (let j = 0; j < cols.length; j++) {
                // 替换双引号并处理CSV格式
                let text = cols[j].innerText.replace(/"/g, '""');
                row.push('"' + text + '"');
            }
            
            csv.push(row.join(','));
        }
        
        // 下载CSV文件
        const csvFile = new Blob([csv.join('\n')], {type: 'text/csv'});
        const downloadLink = document.createElement('a');
        
        downloadLink.download = filename;
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = 'none';
        
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }

    // 图表生成优化
    window.onload = function() {
        const monthlyData = {
            labels: [
                <c:forEach var="row" items="${monthlyData.rows}" varStatus="status">
                '<c:out value="${row.month}" />'${status.last ? '' : ','}
                </c:forEach>
            ],
            datasets: [{
                label: '总金额',
                data: [
                    <c:forEach var="row" items="${monthlyData.rows}" varStatus="status">
                    <c:out value="${row.total_amount}" />${status.last ? '' : ','}
                    </c:forEach>
                ],
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                fill: true,
            }]
        };

        const config = {
            type: 'line',
            data: monthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '月份'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: '总金额'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    title: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        };

        const ctx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(ctx, config);
    };
</script>
<%@ include file="includes/footer.jsp" %>
