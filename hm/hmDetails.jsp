<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.sql.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>
<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<%-- 获取筛选参数 --%>
<c:set var="pageSize" value="10"/>
<c:set var="currentPage" value="${empty param.page ? 1 : param.page}"/>
<fmt:parseNumber var="intCurrentPage" type="number" value="${currentPage}" integerOnly="true"/>
<c:set var="offset" value="${(intCurrentPage - 1) * pageSize}"/>
<c:set var="searchTerm" value="${param.search != null ? param.search : ''}"/>
<c:set var="statusFilter" value="${param.status != null ? param.status : ''}"/>
<c:set var="projectFilter" value="${param.project != null ? param.project : ''}"/>

<%-- 构建WHERE子句 --%>
<c:set var="whereClause" value="WHERE 1=1"/>
<c:if test="${not empty searchTerm}">
    <c:set var="whereClause" value="${whereClause} AND (demographic_name LIKE '%${searchTerm}%' OR demographic_no LIKE '%${searchTerm}%')"/>
</c:if>
<c:if test="${not empty statusFilter}">
    <c:set var="whereClause" value="${whereClause} AND status = '${statusFilter}'"/>
</c:if>
<c:if test="${not empty projectFilter}">
    <c:set var="whereClause" value="${whereClause} AND billing_code = '${projectFilter}'"/>
</c:if>

<%-- 获取总记录数 --%>
<sql:query dataSource="${snapshot}" var="totalRecords">
    SELECT COUNT(*) as total FROM hm_package ${whereClause};
</sql:query>

<%-- 获取分页数据 --%>
<sql:query dataSource="${snapshot}" var="members">
    SELECT * FROM hm_package 
    ${whereClause}
    ORDER BY purchased_date DESC
    LIMIT ${offset}, ${pageSize};
</sql:query>

<%-- 获取所有项目代码，用于筛选 --%>
<sql:query dataSource="${snapshot}" var="projects">
    SELECT DISTINCT billing_code FROM hm_package ORDER BY billing_code;
</sql:query>

<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细数据 - 健康管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
<!-- Sidebar -->
<aside class="w-64 bg-blue-700 text-white p-5 shadow-lg">
    <h2 class="text-2xl font-bold mb-6">健康管理</h2>
    <nav>
        <c:set var="currentUri" value="${fn:substringAfter(pageContext.request.requestURI, '/hm/')}"/>
        <ul class="space-y-4">
            <li>
                <a href="./hmOverview.jsp" 
                   class="block p-3 rounded-lg transition ${currentUri == 'hmOverview.jsp' ? 'bg-blue-600 hover:bg-blue-500' : 'hover:bg-blue-600'}">
                    📊 概览
                </a>
            </li>
            <li>
                <a href="./hmFamilyStats.jsp" 
                   class="block p-3 rounded-lg transition ${currentUri == 'hmFamilyStats.jsp' ? 'bg-blue-600 hover:bg-blue-500' : 'hover:bg-blue-600'}">
                    👨‍👩‍👧‍👦 家庭统计
                </a>
            </li>
            <li>
                <a href="./hmTimeStats.jsp" 
                   class="block p-3 rounded-lg transition ${currentUri == 'hmTimeStats.jsp' ? 'bg-blue-600 hover:bg-blue-500' : 'hover:bg-blue-600'}">
                    📅 时间统计
                </a>
            </li>
            <li>
                <a href="./hmProjectStats.jsp" 
                   class="block p-3 rounded-lg transition ${currentUri == 'hmProjectStats.jsp' ? 'bg-blue-600 hover:bg-blue-500' : 'hover:bg-blue-600'}">
                    📈 项目统计
                </a>
            </li>
            <li>
                <a href="./hmDetails.jsp" 
                   class="block p-3 rounded-lg transition ${currentUri == 'hmDetails.jsp' ? 'bg-blue-600 hover:bg-blue-500' : 'hover:bg-blue-600'}">
                    📋 详细数据
                </a>
            </li>
        </ul>
    </nav>
</aside>

        <!-- Main Content -->
        <div class="flex-1 p-10 overflow-auto">
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-3xl font-bold">详细数据</h1>
                <div class="flex space-x-4">
                    <button onclick="exportData()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        导出数据
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
                <form id="filterForm" method="get" class="grid grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                        <input type="text" name="search" value="${param.search}"
                               placeholder="会员姓名/编号"
                               class="w-full p-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                        <select name="status" class="w-full p-2 border rounded-lg">
                            <option value="">全部状态</option>
                            <option value="active" ${param.status == 'active' ? 'selected' : ''}>活跃</option>
                            <option value="expired" ${param.status == 'expired' ? 'selected' : ''}>已过期</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">项目</label>
                        <select name="project" class="w-full p-2 border rounded-lg">
                            <option value="">全部项目</option>
                            <c:forEach var="project" items="${projects.rows}">
                                <option value="${project.billing_code}" 
                                        ${param.project == project.billing_code ? 'selected' : ''}>
                                    ${project.billing_code}
                                </option>
                            </c:forEach>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">日期范围</label>
                        <input type="text" id="dateRange" name="dateRange" 
                               class="w-full p-2 border rounded-lg"
                               placeholder="选择日期范围">
                    </div>
                    <div class="col-span-4">
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            应用筛选
                        </button>
                        <button type="button" onclick="resetFilters()" 
                                class="ml-2 px-4 py-2 border rounded-lg hover:bg-gray-100">
                            重置
                        </button>
                    </div>
                </form>
            </div>
                        <!-- Data Table -->
                        <div class="bg-white rounded-lg shadow-lg">
                            <div class="p-6">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="p-3 border">ID</th>
                                            <th class="p-3 border">会员编号</th>
                                            <th class="p-3 border">会员姓名</th>
                                            <th class="p-3 border">项目代码</th>
                                            <th class="p-3 border">金额</th>
                                            <th class="p-3 border">状态</th>
                                            <th class="p-3 border">购买日期</th>
                                            <th class="p-3 border">续费状态</th>
                                            <th class="p-3 border">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="row" items="${members.rows}">
                                            <tr class="hover:bg-gray-50">
                                                <td class="p-3 border">${row.id}</td>
                                                <td class="p-3 border">${row.demographic_no}</td>
                                                <td class="p-3 border">${row.demographic_name}</td>
                                                <td class="p-3 border">${row.billing_code}</td>
                                                <td class="p-3 border">¥${row.billing_amount}</td>
                                                <td class="p-3 border">
                                                    <span class="px-2 py-1 rounded-full text-sm
                                                        ${row.status == 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                                        ${row.status == 'active' ? '活跃' : '已过期'}
                                                    </span>
                                                </td>
                                                <td class="p-3 border">${row.purchased_date}</td>
                                                <td class="p-3 border">
                                                    <span class="px-2 py-1 rounded-full text-sm
                                                        ${row.renewed ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                                        ${row.renewed ? '已续费' : '未续费'}
                                                    </span>
                                                </td>
                                                <td class="p-3 border">
                                                    <button onclick="viewDetails(${row.id})" 
                                                            class="text-blue-600 hover:text-blue-800">
                                                        查看
                                                    </button>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
            
                                <!-- Pagination -->
                                <div class="mt-4 flex items-center justify-between">
                                    <div class="text-sm text-gray-700">
                                        显示 ${offset + 1} 到 ${Math.min(offset + pageSize, totalRecords.rows[0].total)} 条，
                                        共 ${totalRecords.rows[0].total} 条记录
                                    </div>
                                    <div class="flex space-x-2">
                                        <%-- 计算总页数 --%>
                                        <c:set var="totalRecordsCount" value="${totalRecords.rows[0].total}"/>
                                        <fmt:parseNumber var="totalPages" value="${Math.ceil(totalRecordsCount / pageSize)}" integerOnly="true"/>
                                        
                                        <%-- 首页 --%>
                                        <a href="?page=1&search=${param.search}&status=${param.status}&project=${param.project}" 
                                           class="px-3 py-1 border rounded-lg ${intCurrentPage == 1 ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'}">
                                            1
                                        </a>
            
                                        <%-- 省略号 --%>
                                        <c:if test="${intCurrentPage > 4}">
                                            <span class="px-3 py-1">...</span>
                                        </c:if>
            
                                        <%-- 当前页前后的页码 --%>
                                        <c:set var="beginPage" value="${intCurrentPage - 2 < 2 ? 2 : intCurrentPage - 2}"/>
                                        <c:set var="endPage" value="${intCurrentPage + 2 > totalPages - 1 ? totalPages - 1 : intCurrentPage + 2}"/>
                                        
                                        <c:forEach var="i" begin="${beginPage}" end="${endPage}">
                                            <c:if test="${i > 1 && i < totalPages}">
                                                <a href="?page=${i}&search=${param.search}&status=${param.status}&project=${param.project}" 
                                                   class="px-3 py-1 border rounded-lg ${intCurrentPage == i ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'}">
                                                    ${i}
                                                </a>
                                            </c:if>
                                        </c:forEach>
            
                                        <%-- 省略号 --%>
                                        <c:if test="${intCurrentPage < totalPages - 3}">
                                            <span class="px-3 py-1">...</span>
                                        </c:if>
            
                                        <%-- 最后一页 --%>
                                        <c:if test="${totalPages > 1}">
                                            <a href="?page=${totalPages}&search=${param.search}&status=${param.status}&project=${param.project}" 
                                               class="px-3 py-1 border rounded-lg ${intCurrentPage == totalPages ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'}">
                                                ${totalPages}
                                            </a>
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            
                <script>
                    // 初始化日期选择器
                    $(function() {
                        $('#dateRange').daterangepicker({
                            opens: 'left',
                            locale: {
                                format: 'YYYY-MM-DD',
                                applyLabel: '确定',
                                cancelLabel: '取消',
                                fromLabel: '从',
                                toLabel: '至',
                                customRangeLabel: '自定义',
                                weekLabel: 'W',
                                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                                monthNames: ['一月', '二月', '三月', '四月', '五月', '六月',
                                           '七月', '八月', '九月', '十月', '十一月', '十二月'],
                                firstDay: 1
                            }
                        });
                    });
            
                    // 重置筛选条件
                    function resetFilters() {
                        document.getElementById('filterForm').reset();
                        window.location.href = 'hmDetails.jsp';
                    }
            
                    // 查看详情
                    function viewDetails(id) {
                        // TODO: 实现查看详情功能
                        alert('查看详情功能开发中...');
                    }
            
                    // 导出数据
                    function exportData() {
                        // TODO: 实现导出功能
                        alert('导出功能开发中...');
                    }
                </script>
            </body>
            </html>