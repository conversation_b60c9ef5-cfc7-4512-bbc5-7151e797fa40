<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.sql.*, java.util.*, java.text.SimpleDateFormat" %>
<%@ page import="javax.servlet.http.*, javax.servlet.*" %>
<%@ page import="java.util.Date" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    request.setAttribute("pageTitle", "家庭会员管理");

    // Database connection and query parameters
    String memberLevel = request.getParameter("memberLevel");
    if (memberLevel == null || memberLevel.isEmpty()) memberLevel = "AHM_%";

    String startDate = request.getParameter("startDate");
    if (startDate == null || startDate.isEmpty()) startDate = "2023";
    
    // Use today variable from hmAuth.jsp
    String endDate = request.getParameter("endDate");
    if (endDate == null || endDate.isEmpty()) endDate = today;

    String sortField = request.getParameter("sortField");
    String sortOrder = request.getParameter("sortOrder");
    if (sortField == null || sortField.isEmpty()) sortField = "join_date";
    if (sortOrder == null || sortOrder.isEmpty()) sortOrder = "ASC";
    
    request.setAttribute("memberLevel", memberLevel);
    request.setAttribute("startDate", startDate);
    request.setAttribute("endDate", endDate);
    request.setAttribute("sortField", sortField);
    request.setAttribute("sortOrder", sortOrder);
%>

<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<!-- Main query for family members -->
<sql:query dataSource="${snapshot}" var="familyMembers">
    SELECT DISTINCT
        f.family_id,
        d.demographic_no,
        CONCAT(d.last_name, ', ', d.first_name) AS member_name,
        d.hin,
        (YEAR(CURDATE()) - d.year_of_birth) AS age,
        DATE_FORMAT(bm.service_date, '%Y-%m-%d') AS join_date,
        bm.billing_code,
        bm.bill_amount,
        (SELECT GROUP_CONCAT(DISTINCT CONCAT(d2.first_name, ' ', d2.last_name) SEPARATOR ', ')
         FROM hm_family_members fm2
         JOIN demographic d2 ON fm2.demographic_no = d2.demographic_no
         WHERE fm2.family_id = f.family_id AND fm2.demographic_no != d.demographic_no AND fm2.deleted = '0'
        ) AS family_members_list
    FROM hm_family_info f
    JOIN hm_family_members fm ON f.family_id = fm.family_id
    JOIN demographic d ON fm.demographic_no = d.demographic_no
    LEFT JOIN billingmaster bm ON d.demographic_no = bm.demographic_no AND bm.billing_code LIKE 'AHM\_%' AND bm.billingstatus != 'D'
    WHERE f.deleted = '0' AND fm.deleted = '0'
    AND bm.billing_code LIKE ?
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') >= ?
    AND DATE_FORMAT(bm.service_date, '%Y-%m-%d') <= ?
    ORDER BY ${sortField} ${sortOrder};
    <sql:param value="${memberLevel}" />
    <sql:param value="${startDate}" />
    <sql:param value="${endDate}" />
</sql:query>

<!-- Query for monthly family member additions -->
<sql:query dataSource="${snapshot}" var="monthlyFamilyData">
    SELECT 
        DATE_FORMAT(f.created_by, '%Y-%m') AS month, 
        COUNT(DISTINCT f.family_id) AS new_families,
        COUNT(DISTINCT fm.demographic_no) AS new_members_in_families
    FROM hm_family_info f
    JOIN hm_family_members fm ON f.family_id = fm.family_id
    WHERE f.deleted = '0' AND fm.deleted = '0'
    AND f.created_by >= DATE_FORMAT(CONCAT(?, '-01-01'), '%Y-%m-%d') 
    AND f.created_by <= DATE_FORMAT(CONCAT(?, '-12-31'), '%Y-%m-%d')
    GROUP BY DATE_FORMAT(f.created_by, '%Y-%m')
    ORDER BY month;
    <sql:param value="${fn:substring(startDate, 0, 4)}" />
    <sql:param value="${fn:substring(endDate, 0, 4)}" />
</sql:query>

<%@ include file="includes/header.jsp" %>

<!-- Body tag is opened in header.jsp, container and row are managed by this page -->
<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">家庭会员管理</h2>
                 <span class="text-muted">当前日期: <%= today %></span>
            </div>

            <form method="get" class="row g-3 mb-4">
                <div class="col-md-3">
                    <label for="memberLevel" class="form-label text-danger">会员级别</label>
                    <select name="memberLevel" id="memberLevel" class="form-select">
                        <option value="AHM_%" ${memberLevel == 'AHM_%' ? 'selected' : ''}>所有健康管理会员</option>
                        <option value="AHM_SIG%" ${memberLevel == 'AHM_SIG%' ? 'selected' : ''}>所有 SIG 会员</option>
                        <option value="AHM_EM%" ${memberLevel == 'AHM_EM%' ? 'selected' : ''}>所有 EM 会员</option>
                        <option value="AHM_VIP%" ${memberLevel == 'AHM_VIP%' ? 'selected' : ''}>所有 VIP 会员</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="startDate" class="form-label text-danger">开始年份</label>
                    <input type="text" name="startDate" id="startDate" value="${startDate}" class="form-control" placeholder="YYYY">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label text-danger">结束日期</label>
                    <input type="date" name="endDate" id="endDate" value="${endDate}" class="form-control">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-danger w-100">筛选</button>
                </div>
                <input type="hidden" name="sortField" id="sortFieldInput" value="${sortField}" />
                <input type="hidden" name="sortOrder" id="sortOrderInput" value="${sortOrder}" />
            </form>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">月度家庭会员增长</h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px;">
                        <canvas id="monthlyFamilyChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">家庭会员列表</h5>
                    <span class="badge bg-primary rounded-pill">${familyMembers.rowCount} 位会员</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover my-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="sortable" data-field="f.family_id">家庭ID <i class="bi bi-sort-alpha-down"></i></th>
                                    <th class="sortable" data-field="d.demographic_no">会员ID</th>
                                    <th>会员姓名</th>
                                    <th class="sortable" data-field="d.hin">MSP #</th>
                                    <th class="sortable" data-field="age">年龄</th>
                                    <th class="sortable" data-field="join_date">加入日期</th>
                                    <th>会员级别</th>
                                    <th>金额</th>
                                    <th>其他家庭成员</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:set var="totalAmount" value="0" />
                                <c:forEach var="member" items="${familyMembers.rows}" varStatus="status">
                                    <tr>
                                        <td>${member.family_id}</td>
                                        <td>${member.demographic_no}</td>
                                        <td><a href="https://oscar.mmcwellness.ca/oscar/demographic/demographiccontrol.jsp?demographic_no=${fn:escapeXml(member.demographic_no)}&displaymode=edit&dboperation=search_detail" class="text-decoration-none">${fn:escapeXml(member.member_name)}</a></td>
                                        <td>${fn:escapeXml(member.hin)}</td>
                                        <td>${member.age}</td>
                                        <td>${member.join_date}</td>
                                        <td><span class="badge bg-info">${fn:escapeXml(member.billing_code)}</span></td>
                                        <td><fmt:formatNumber value="${member.bill_amount}" type="currency" currencySymbol="$"/></td>
                                        <td>${fn:escapeXml(member.family_members_list)}</td>
                                        <c:set var="totalAmount" value="${totalAmount + member.bill_amount}" />
                                    </tr>
                                </c:forEach>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <td colspan="7" class="text-end fw-bold">总金额:</td>
                                    <td colspan="2" class="fw-bold"><fmt:formatNumber value="${totalAmount}" type="currency" currencySymbol="$"/></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div> <!-- main-content -->
    </div> <!-- row -->
</div> <!-- container-fluid -->

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Chart for monthly family member additions
        const monthlyFamilyCtx = document.getElementById('monthlyFamilyChart').getContext('2d');
        const monthlyFamilyLabels = [<c:forEach var="data" items="${monthlyFamilyData.rows}" varStatus="status">'${data.month}'${!status.last ? ',' : ''}</c:forEach>];
        const monthlyNewFamilies = [<c:forEach var="data" items="${monthlyFamilyData.rows}" varStatus="status">${data.new_families}${!status.last ? ',' : ''}</c:forEach>];
        const monthlyNewMembersInFamilies = [<c:forEach var="data" items="${monthlyFamilyData.rows}" varStatus="status">${data.new_members_in_families}${!status.last ? ',' : ''}</c:forEach>];

        new Chart(monthlyFamilyCtx, {
            type: 'bar',
            data: {
                labels: monthlyFamilyLabels,
                datasets: [
                    {
                        label: '新增家庭数',
                        data: monthlyNewFamilies,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '家庭中新增会员数',
                        data: monthlyNewMembersInFamilies,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: '数量' }
                    },
                    x: {
                         title: { display: true, text: '月份' }
                    }
                }
            }
        });

        // Sorting logic
        const sortableHeaders = document.querySelectorAll('.sortable');
        const sortFieldInput = document.getElementById('sortFieldInput');
        const sortOrderInput = document.getElementById('sortOrderInput');
        const currentSortField = '${sortField}';
        const currentSortOrder = '${sortOrder}';

        sortableHeaders.forEach(header => {
            const field = header.dataset.field;
            const icon = header.querySelector('i');
            if (icon) { // Ensure icon exists
                 if (field === currentSortField) {
                    icon.className = currentSortOrder === 'ASC' ? 'bi bi-sort-alpha-down' : 'bi bi-sort-alpha-up';
                } else {
                    icon.className = 'bi bi-filter'; // Default icon or placeholder
                }
            }

            header.addEventListener('click', function () {
                let newOrder = 'ASC';
                if (sortFieldInput.value === field) {
                    newOrder = sortOrderInput.value === 'ASC' ? 'DESC' : 'ASC';
                }
                sortFieldInput.value = field;
                sortOrderInput.value = newOrder;
                header.closest('form').submit();
            });
        });
    });
</script>
<%@ include file="includes/footer.jsp" %>
