<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.Enumeration" %>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>
<html>
<head><title>JDBC Driver Test</title></head>
<body>
    <h1>JDBC Driver Test Page</h1>

    <h2>1. Attempting to load MySQL CJ Driver:</h2>
    <%
        String driverClass = "com.mysql.cj.jdbc.Driver";
        try {
            Class.forName(driverClass);
            out.println("<p style=\"color:green;\">SUCCESS: Driver class '" + driverClass + "' loaded successfully!</p>");
        } catch (ClassNotFoundException e) {
            out.println("<p style=\"color:red;\">ERROR: Driver class '" + driverClass + "' NOT FOUND.</p>");
            out.println("<pre>");
            e.printStackTrace(new java.io.PrintWriter(out));
            out.println("</pre>");
        }
    %>

    <h2>2. Listing Registered JDBC Drivers:</h2>
    <%
        try {
            Enumeration<Driver> drivers = DriverManager.getDrivers();
            boolean foundMySQLDriver = false;
            if (!drivers.hasMoreElements()) {
                out.println("<p style=\"color:orange;\">No JDBC drivers are registered with DriverManager.</p>");
            } else {
                out.println("<ul>");
                while (drivers.hasMoreElements()) {
                    Driver d = drivers.nextElement();
                    out.println("<li>" + d.getClass().getName() + " (Version: " + d.getMajorVersion() + "." + d.getMinorVersion() + ")</li>");
                    if (d.getClass().getName().equals(driverClass)) {
                        foundMySQLDriver = true;
                    }
                }
                out.println("</ul>");
                if (foundMySQLDriver) {
                    out.println("<p style=\"color:green;\">MySQL CJ driver is registered with DriverManager.</p>");
                } else {
                    out.println("<p style=\"color:orange;\">MySQL CJ driver ('" + driverClass + "') is NOT among the registered drivers.</p>");
                }
            }
        } catch (Exception e) {
            out.println("<p style=\"color:red;\">ERROR listing drivers: " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(new java.io.PrintWriter(out));
            out.println("</pre>");
        }
    %>

    <h2>3. Attempting Direct JDBC Connection (using details from your context.xml):</h2>
    <%
        String jdbcUrl = "**************************"; // From your context.xml
        String dbUser = "root"; // From your context.xml
        String dbPassword = "Z2Rh6VGr7DE="; // From your context.xml
        Connection conn = null;
        try {
            out.println("<p>Attempting connection to: " + jdbcUrl + " with user: " + dbUser + "</p>");
            conn = DriverManager.getConnection(jdbcUrl, dbUser, dbPassword);
            if (conn != null) {
                out.println("<p style=\"color:green;\">SUCCESS: Direct JDBC connection established!</p>");
                out.println("<p>Database Product: " + conn.getMetaData().getDatabaseProductName() + "</p>");
                out.println("<p>Database Version: " + conn.getMetaData().getDatabaseProductVersion() + "</p>");
                conn.close();
                out.println("<p>Connection closed.</p>");
            } else {
                out.println("<p style=\"color:red;\">ERROR: DriverManager.getConnection returned null.</p>");
            }
        } catch (SQLException e) {
            out.println("<p style=\"color:red;\">ERROR during JDBC connection attempt: " + e.getMessage() + "</p>");
            out.println("<p>SQLState: " + e.getSQLState() + "</p>");
            out.println("<p>VendorError: " + e.getErrorCode() + "</p>");
            out.println("<pre>");
            e.printStackTrace(new java.io.PrintWriter(out));
            out.println("</pre>");
             if (e.getMessage().toLowerCase().contains("access denied")) {
                out.println("<p style=\"color:orange;font-weight:bold;\">Hint: This looks like an 'Access Denied' error. Check username/password and database grants.</p>");
            } else if (e.getMessage().toLowerCase().contains("communications link failure") || e.getMessage().toLowerCase().contains("could not connect")) {
                out.println("<p style=\"color:orange;font-weight:bold;\">Hint: This looks like a network connectivity issue. Is the database server 'db' reachable on port 3306 from the Tomcat container? Check Docker networking.</p>");
            }
        } catch (Exception e) {
            out.println("<p style=\"color:red;\">ERROR: Unexpected exception during JDBC connection attempt.</p>");
            out.println("<pre>");
            e.printStackTrace(new java.io.PrintWriter(out));
            out.println("</pre>");
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException ex) {
                    // ignore
                }
            }
        }
    %>
</body>
</html> 