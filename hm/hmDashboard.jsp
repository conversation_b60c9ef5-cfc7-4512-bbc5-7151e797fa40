<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    // Variables currentMonth and yearStart are already declared in hmAuth.jsp
    
    // Set page title for header.jsp
    request.setAttribute("pageTitle", "健康管理会员仪表盘");
%>

<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<!-- 总会员数 - 仅健康管理套餐会员 -->
<sql:query dataSource="${snapshot}" var="total_members">
    SELECT COUNT(DISTINCT demographic_no) AS total 
    FROM billingmaster 
    WHERE UPPER(billing_code) LIKE 'AHM\\_%' 
    AND billingstatus != 'D';
</sql:query>

<!-- 本月新增套餐会员 -->
<sql:query dataSource="${snapshot}" var="monthly_new">
    SELECT COUNT(DISTINCT demographic_no) AS total FROM billingmaster
    WHERE UPPER(billing_code) LIKE 'AHM\\_%' AND billingstatus != 'D'
      AND DATE_FORMAT(service_date, '%Y-%m') = ?;
    <sql:param value="<%=currentMonth%>" />
</sql:query>

<!-- 套餐总收入 -->
<sql:query dataSource="${snapshot}" var="total_revenue">
    SELECT IFNULL(SUM(bill_amount),0) AS total FROM billingmaster 
    WHERE UPPER(billing_code) LIKE 'AHM\\_%' AND billingstatus != 'D';
</sql:query>

<!-- 续费情况统计 -->
<sql:query dataSource="${snapshot}" var="renewal_stats">
    SELECT 
        COUNT(DISTINCT demographic_no) AS total,
        COALESCE(SUM(renewal_count > 1), 0) AS renewed,
        COALESCE(SUM(renewal_count = 1 AND last_renewal > DATE_SUB(CURDATE(), INTERVAL 1 YEAR)), 0) AS new_members
    FROM (
        SELECT 
            demographic_no,
            COUNT(billing_code) AS renewal_count,
            MAX(service_date) AS last_renewal
        FROM billingmaster
        WHERE UPPER(billing_code) LIKE 'AHM\\_%' AND billingstatus != 'D'
        GROUP BY demographic_no
    ) AS member_stats;
</sql:query>

<!-- 家庭会员数量 -->
<sql:query dataSource="${snapshot}" var="family_members">
    SELECT COUNT(DISTINCT family.family_id) AS total
    FROM (
        SELECT 
            r.demographic_no, 
            MIN(LEAST(r.demographic_no, r.relation_demographic_no)) AS family_id
        FROM relationships r
        WHERE (r.deleted IS NULL OR r.deleted != '1')
        GROUP BY r.demographic_no
    ) AS family
    INNER JOIN billingmaster b ON b.demographic_no = family.demographic_no
    WHERE b.billing_code LIKE 'AHM%' AND b.billingstatus != 'D';
</sql:query>

<!-- 原始统计方式 -->
<sql:query dataSource="${snapshot}" var="old_family_members">
    SELECT COUNT(DISTINCT family.family_id) AS total
    FROM (
        SELECT 
            r.demographic_no, 
            MIN(LEAST(r.demographic_no, r.relation_demographic_no)) AS family_id
        FROM relationships r
        WHERE (r.deleted IS NULL OR r.deleted != '1')
        GROUP BY r.demographic_no
    ) AS family
    INNER JOIN billingmaster b ON b.demographic_no = family.demographic_no
    WHERE UPPER(b.billing_code) LIKE 'AHM\\_%' AND b.billingstatus != 'D';
</sql:query>

<!-- 会员分析 - 实际拥有健康管理套餐的会员 -->
<sql:query dataSource="${snapshot}" var="member_analysis">
    SELECT 
        COUNT(DISTINCT demographic_no) AS total,
        COUNT(DISTINCT CASE WHEN billing_code LIKE 'AHM%' THEN demographic_no END) AS ahm_members,
        COUNT(DISTINCT CASE WHEN UPPER(billing_code) LIKE 'AHM\\_%' THEN demographic_no END) AS ahm_underscore
    FROM billingmaster
    WHERE billingstatus != 'D';
</sql:query>

<!-- 今年月度收入数据 -->
<sql:query dataSource="${snapshot}" var="monthly_revenue">
    SELECT 
        DATE_FORMAT(service_date, '%Y-%m') AS month,
        SUM(bill_amount) AS revenue
    FROM billingmaster
    WHERE UPPER(billing_code) LIKE 'AHM\\_%' 
    AND billingstatus != 'D'
    AND service_date >= ?
    GROUP BY DATE_FORMAT(service_date, '%Y-%m')
    ORDER BY month;
    <sql:param value="<%=yearStart%>" />
</sql:query>

<%@ include file="includes/header.jsp" %>

<!-- Specific styles for hmMenu.jsp -->
<style>
    .dashboard-card {
        transition: all 0.3s;
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
    .stats-card {
        text-align: center;
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    .stats-card .icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        display: inline-block;
        padding: 15px;
        border-radius: 50%;
        color: white;
    }
    .chart-container {
        height: 300px;
        width: 100%;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">健康管理会员仪表盘</h2>
                <span class="text-muted">当前日期: <%= today %></span>
            </div>
            
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-primary">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h5>健康管理会员总数</h5>
                        <h3 class="fw-bold">${total_members.rows[0].total}</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-success">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <h5>本月新增会员</h5>
                        <h3 class="fw-bold">${monthly_new.rows[0].total}</h3>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-info">
                            <i class="bi bi-people"></i>
                        </div>
                        <h5>家庭会员数</h5>
                        <h3 class="fw-bold">${family_members.rows[0].total}</h3>
                        <small class="text-muted">原统计: ${old_family_members.rows[0].total}</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="icon bg-warning">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <h5>套餐总收入</h5>
                        <h3 class="fw-bold">$<fmt:formatNumber value="${total_revenue.rows[0].total}" pattern="#,##0.00"/></h3>
                    </div>
                </div>
            </div>
            
            <!-- 第二行卡片 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title">月度收入</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-white">
                            <h5 class="card-title">会员续费情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="renewalChart"></canvas>
                            </div>
                            <div class="text-center mt-3">
                                <a href="renewals.jsp" class="btn btn-outline-primary">查看续费详情</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快速导航卡片 -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="rounded-circle bg-primary p-3 text-white me-3">
                                    <i class="bi bi-people-fill"></i>
                                </div>
                                <h5 class="card-title mb-0">会员管理</h5>
                            </div>
                            <p class="card-text">查看所有会员详细信息，包括会员级别、缴费记录和最近的预约情况。</p>
                            <a href="members.jsp" class="btn btn-primary w-100">管理会员</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="rounded-circle bg-success p-3 text-white me-3">
                                    <i class="bi bi-arrow-repeat"></i>
                                </div>
                                <h5 class="card-title mb-0">会员续费</h5>
                            </div>
                            <p class="card-text">管理会员续费状态，查看已续费、新会员和需要续费的会员列表。</p>
                            <a href="renewals.jsp" class="btn btn-success w-100">续费管理</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="rounded-circle bg-info p-3 text-white me-3">
                                    <i class="bi bi-people"></i>
                                </div>
                                <h5 class="card-title mb-0">家庭会员</h5>
                            </div>
                            <p class="card-text">查看和管理家庭会员套餐，包括家庭成员关联和套餐使用情况。</p>
                            <a href="membersNew.jsp" class="btn btn-info w-100">家庭会员管理</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 增加数据元素，用于传递统计数据到JavaScript -->
<div id="renewal-data" 
    data-renewed="${renewal_stats.rows[0].renewed}" 
    data-new-members="${renewal_stats.rows[0].new_members}" 
    data-total="${renewal_stats.rows[0].total}" 
    class="d-none"></div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 月度收入图表
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: [
                    <c:forEach var="item" items="${monthly_revenue.rows}" varStatus="status">
                    '${item.month}'${!status.last ? ',' : ''}
                    </c:forEach>
                ],
                datasets: [{
                    label: '月度收入 ($)',
                    data: [
                        <c:forEach var="item" items="${monthly_revenue.rows}" varStatus="status">
                        ${item.revenue}${!status.last ? ',' : ''}
                        </c:forEach>
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });
        
        // 续费情况饼图 - 从数据元素获取统计数据
        const renewalCtx = document.getElementById('renewalChart').getContext('2d');
        const renewalData = document.getElementById('renewal-data');
        
        const renewedCount = parseInt(renewalData.dataset.renewed) || 0;
        const newMembersCount = parseInt(renewalData.dataset.newMembers) || 0;
        const totalCount = parseInt(renewalData.dataset.total) || 0;
        const notRenewedCount = totalCount - renewedCount - newMembersCount;
        
        new Chart(renewalCtx, {
            type: 'doughnut',
            data: {
                labels: ['已续费', '新会员', '未续费'],
                datasets: [{
                    data: [renewedCount, newMembersCount, notRenewedCount],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)', // 绿色 - 已续费
                        'rgba(0, 123, 255, 0.7)', // 蓝色 - 新会员
                        'rgba(255, 193, 7, 0.7)'  // 黄色 - 未续费
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });
</script>
<%@ include file="includes/footer.jsp" %> 