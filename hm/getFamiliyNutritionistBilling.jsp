<%@ page import="java.sql.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>

<!-- Include authentication check for API endpoint -->
<%
    // Check if user is logged into Oscar first
    String loggedInUser = (String) session.getAttribute("user");
    if (loggedInUser == null || loggedInUser.trim().isEmpty()) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Not logged into Oscar system. Please login first.\"}");
        return;
    }

    // API authentication check for HM module
    String authenticated = (String) session.getAttribute("hmMenuAuthenticated");
    if (!"true".equals(authenticated)) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Unauthorized access to HM system. Please login to HM module first.\"}");
        return;
    }
%>
<%@ page contentType="application/json; charset=UTF-8" %>

<%!
    // Helper function for JSON string escaping
    private String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\").replace("\"", "\\\"").replace("\b", "\\b")
                  .replace("\f", "\\f").replace("\n", "\\n").replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
%>

<%
    String demoNoStr = request.getParameter("demographic_no");
    StringBuilder jsonOutput = new StringBuilder("[");

    if (demoNoStr == null || demoNoStr.trim().isEmpty()) {
        out.print("{\"error\":\"Missing demographic_no parameter.\"}");
        return;
    }

    int demographicNo = 0;
    try {
        demographicNo = Integer.parseInt(demoNoStr);
    } catch (NumberFormatException e) {
        out.print("{\"error\":\"Invalid demographic_no parameter.\"}");
        return;
    }
%>

<sql:setDataSource var="dbSource" dataSource="jdbc/CustomOscarDB" />

<sql:query var="memberData" dataSource="${dbSource}">
    SELECT DISTINCT 
        p.demographic_no AS member_demographic_no,
        d.first_name,
        d.last_name,
        CASE 
            WHEN p.demographic_no = ? THEN 'Self'
            ELSE COALESCE(
                (SELECT 
                    CASE r.relation
                        WHEN 'Son' THEN 'Father'
                        WHEN 'Daughter' THEN 'Father'
                        WHEN 'Father' THEN 'Son'
                        WHEN 'Mother' THEN 'Son'
                        WHEN 'Wife' THEN 'Husband'
                        WHEN 'Husband' THEN 'Wife'
                        WHEN 'Brother' THEN 'Brother'
                        WHEN 'Sister' THEN 'Sister'
                        ELSE r.relation
                    END
                 FROM relationships r 
                 WHERE r.demographic_no = ? AND r.relation_demographic_no = p.demographic_no
                 LIMIT 1), 
                COALESCE(
                    (SELECT r.relation 
                     FROM relationships r 
                     WHERE r.relation_demographic_no = ? AND r.demographic_no = p.demographic_no
                     LIMIT 1), 
                    'Family Member'
                )
            )
        END AS relationship
    FROM (
        SELECT demographic_no FROM relationships 
        WHERE relation_demographic_no = ? AND (deleted IS NULL OR deleted != '1')
        
        UNION
        
        SELECT relation_demographic_no AS demographic_no FROM relationships 
        WHERE demographic_no = ? AND (deleted IS NULL OR deleted != '1')
        
        UNION
        
        SELECT ? AS demographic_no
    ) AS p
    LEFT JOIN demographic d ON d.demographic_no = p.demographic_no
    ORDER BY 
        CASE WHEN p.demographic_no = ? THEN 0 ELSE 1 END,
        d.last_name, d.first_name
    <sql:param value="${param.demographic_no}" />
    <sql:param value="${param.demographic_no}" />
    <sql:param value="${param.demographic_no}" />
    <sql:param value="${param.demographic_no}" />
    <sql:param value="${param.demographic_no}" />
    <sql:param value="${param.demographic_no}" />
    <sql:param value="${param.demographic_no}" />
</sql:query>

<%
    javax.servlet.jsp.jstl.sql.Result memberDataResult = 
        (javax.servlet.jsp.jstl.sql.Result) pageContext.findAttribute("memberData");

    if (memberDataResult != null && memberDataResult.getRowCount() > 0) {
        boolean first = true;

        for (Object[] row : memberDataResult.getRowsByIndex()) {
            if (!first) {
                jsonOutput.append(",");
            }
            first = false;
            
            int memberDemoNo;
            if (row[0] instanceof Number) {
                memberDemoNo = ((Number) row[0]).intValue();
            } else {
                memberDemoNo = Integer.parseInt(row[0].toString());
            }
            String firstName = (row[1] != null) ? row[1].toString() : "";
            String lastName = (row[2] != null) ? row[2].toString() : "";
            String relationship = (row[3] != null) ? row[3].toString() : "Unknown";
%>
            <sql:query var="billingData" dataSource="${dbSource}">
                SELECT COUNT(*) as count FROM billingmaster WHERE demographic_no = ? AND billing_code = 'AHM_NCN'
                <sql:param value="<%=memberDemoNo%>" />
            </sql:query>
<%
            javax.servlet.jsp.jstl.sql.Result billingDataResult = 
                (javax.servlet.jsp.jstl.sql.Result) pageContext.findAttribute("billingData");
            
            String hasNutritionist = "NO";
            if (billingDataResult != null && billingDataResult.getRowCount() > 0) {
                Object[] billingRow = billingDataResult.getRowsByIndex()[0];
                int count;
                if (billingRow[0] instanceof Number) {
                    count = ((Number) billingRow[0]).intValue();
                } else {
                    count = Integer.parseInt(billingRow[0].toString());
                }
                if (count > 0) {
                    hasNutritionist = "YES";
                }
            }
            
            String fullName = "";
            if (firstName != null && lastName != null) {
                fullName = firstName + " " + lastName;
            } else if (firstName != null) {
                fullName = firstName;
            } else if (lastName != null) {
                fullName = lastName;
            } else {
                fullName = "Unknown";
            }
            
            jsonOutput.append("{");
            jsonOutput.append("\"demographic_no\":").append(memberDemoNo).append(",");
            jsonOutput.append("\"first_name\":\"").append(escapeJson(firstName)).append("\",");
            jsonOutput.append("\"last_name\":\"").append(escapeJson(lastName)).append("\",");
            jsonOutput.append("\"has_nutritionist_billing\":\"").append(escapeJson(hasNutritionist)).append("\",");
            jsonOutput.append("\"relationship\":\"").append(escapeJson(relationship)).append("\",");
            jsonOutput.append("\"full_name\":\"").append(escapeJson(fullName)).append("\"");
            jsonOutput.append("}");
        }

    } else {
        // No patient found
        out.print("{\"error\":\"Patient not found.\"}");
        return;
    }
    
    jsonOutput.append("]");
    out.print(jsonOutput.toString());
    out.flush();
%> 