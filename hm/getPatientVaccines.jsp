<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<!-- API authentication check -->
<%
    // Check if user is logged into Oscar first
    String loggedInUser = (String) session.getAttribute("user");
    if (loggedInUser == null || loggedInUser.trim().isEmpty()) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Not logged into Oscar system. Please login first.\"}");
        return;
    }

    // API authentication check for HM module
    String authenticated = (String) session.getAttribute("hmMenuAuthenticated");
    if (!"true".equals(authenticated)) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Unauthorized access to HM system. Please login to HM module first.\"}");
        return;
    }
%>
<%@ page contentType="application/json; charset=UTF-8" %>

<c:set var="demographicNo" value="${param.demographic_no}" />

<sql:setDataSource var="dataSource" dataSource="jdbc/CustomOscarDB" />

<c:if test="${not empty demographicNo}">
    <sql:query var="vaccines" dataSource="${dataSource}">
        SELECT DISTINCT
            p.id,
            p.prevention_type,
            CONCAT(d.first_name, ' ', d.last_name) AS name,
            CONCAT('Dr. ', pr.last_name, ',', REGEXP_REPLACE(pr.first_name, '_[0-9]+$', '')) as doctor,
            CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')) AS birthday,
            DATE_FORMAT(p.prevention_date, '%Y-%m-%d') AS prevention_date,

            DATEDIFF(p.prevention_date,
                    CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0'))
            ) AS dayDiff,

            FLOOR(DATEDIFF(p.prevention_date,
                    CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0'))
            ) / 7) AS weekDiff,

            TIMESTAMPDIFF(MONTH,
                CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                p.prevention_date
            ) AS totalMonthDiff,

            TIMESTAMPDIFF(YEAR,
                CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                p.prevention_date
            ) AS yearDiff,

            (TIMESTAMPDIFF(MONTH,
                CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                p.prevention_date
            ) - TIMESTAMPDIFF(YEAR,
                CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                p.prevention_date
            ) * 12) AS remainingMonths,

            CASE
                WHEN TIMESTAMPDIFF(MONTH,
                    CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                    p.prevention_date
                ) < 12
                THEN CONCAT(
                    TIMESTAMPDIFF(MONTH,
                        CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                        p.prevention_date
                    ),
                    ' months'
                )
                ELSE CONCAT(
                    TIMESTAMPDIFF(YEAR,
                        CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                        p.prevention_date),
                    ' years ',
                    (TIMESTAMPDIFF(MONTH,
                        CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                        p.prevention_date
                    ) - TIMESTAMPDIFF(YEAR,
                        CONCAT(year_of_birth, '-', LPAD(month_of_birth, 2, '0'), '-', LPAD(date_of_birth, 2, '0')),
                        p.prevention_date
                    ) * 12),
                    ' months'
                )
            END AS ageText,

            p.prevention_type AS prevention_type,
            p.refused,
            p.programNo,
            pe.keyval,
            pe.val

        FROM preventions p
        INNER JOIN demographic d ON d.demographic_no = p.demographic_no
        INNER JOIN provider pr ON pr.provider_no = p.provider_no
        LEFT JOIN preventionsExt pe ON p.id = pe.prevention_id
        WHERE p.demographic_no = ? AND p.deleted = 0
        ORDER BY p.prevention_date DESC
        <sql:param value="${demographicNo}" />
    </sql:query>
</c:if>

[
<c:forEach var="vaccine" items="${vaccines.rows}" varStatus="status">
    {
        "id": ${vaccine.id},
        "name": "${vaccine.name}",
        "doctor": "${vaccine.doctor}",
        "birthday": "${vaccine.birthday}",
        "prevention_date": "${vaccine.prevention_date}",
        "dayDiff": ${vaccine.dayDiff},
        "weekDiff": ${vaccine.weekDiff},
        "totalMonthDiff": ${vaccine.totalMonthDiff},
        "yearDiff": ${vaccine.yearDiff},
        "remainingMonths": ${vaccine.remainingMonths},
        "ageText": "${vaccine.ageText}",
        "prevention_type": "${vaccine.prevention_type}",
        "refused": ${vaccine.refused},
        "programNo": "${vaccine.programNo}",
        "keyval": "${vaccine.keyval}",
        "val": "${vaccine.val}"
    }<c:if test="${!status.last}">,</c:if>
</c:forEach>
]
