<%@ page import="java.text.SimpleDateFormat, java.util.TimeZone, java.util.List, java.util.ArrayList, java.util.Map, java.util.LinkedHashMap, java.net.HttpURLConnection, java.net.URL, java.net.URLEncoder, java.io.BufferedReader, java.io.InputStreamReader, java.io.OutputStream, java.nio.charset.StandardCharsets, java.io.IOException" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>

<!-- API authentication check -->
<%
    // Check if user is logged into Oscar first
    String loggedInUser = (String) session.getAttribute("user");
    if (loggedInUser == null || loggedInUser.trim().isEmpty()) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Not logged into Oscar system. Please login first.\"}");
        return;
    }

    // API authentication check for HM module
    String authenticated = (String) session.getAttribute("hmMenuAuthenticated");
    if (!"true".equals(authenticated)) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Unauthorized access to HM system. Please login to HM module first.\"}");
        return;
    }
%>
<%@ page contentType="application/json; charset=UTF-8" %>
<% System.out.println("--- TOP OF getPatientAppointments.jsp EXECUTING (v4) ---"); %>

<%! // Declaration block for constants and helper methods
    // ============== START: SUPABASE CONFIGURATION ==============
    private static final String SUPABASE_PROJECT_URL = "https://kong.mmcwellness.ca"; 
    private static final String SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU";
    private static final String APPOINTMENT_IMAGE_BUCKET = "patient-images"; 
    private static final int SIGNED_URL_EXPIRES_IN_SECONDS = 3600;
    // ============== END: SUPABASE CONFIGURATION ==============

    private String generateSupabaseSignedUrlViaPython(String objectPath) {
        if (objectPath == null || objectPath.trim().isEmpty()) {
            System.err.println("DEBUG JSP: generateSupabaseSignedUrlViaPython called with null/empty objectPath");
            return "";
        }
        System.out.println("--- JSP: Attempting to generate signed URL for objectPath: " + objectPath);

        String pythonExecutable = "python3";
        String pythonScriptPath = "/usr/local/tomcat/webapps/oscar/hm/getPatientAppointments.py";

        String[] cmd = {
            pythonExecutable,
            pythonScriptPath,
            objectPath,
            SUPABASE_PROJECT_URL, 
            SUPABASE_SERVICE_ROLE_KEY,
            APPOINTMENT_IMAGE_BUCKET,
            String.valueOf(SIGNED_URL_EXPIRES_IN_SECONDS)
        };

        Process process = null;
        BufferedReader reader = null;
        StringBuilder output = new StringBuilder();
        String errorOutput = "";

        try {
            System.out.println("--- JSP: Executing Python script: " + String.join(" ", cmd));
            ProcessBuilder pb = new ProcessBuilder(cmd);
            process = pb.start();

            reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8));
            StringBuilder errorBuilder = new StringBuilder();
            while ((line = errorReader.readLine()) != null) {
                errorBuilder.append(line).append("\n");
            }
            errorOutput = errorBuilder.toString();

            int exitCode = process.waitFor();
            System.out.println("--- JSP: Python script for '" + objectPath + "' exited with code: " + exitCode);
            
            if (errorOutput == null) {
                System.err.println("DEBUG JSP: errorOutput variable is null for objectPath '" + objectPath + "'");
            } else if (errorOutput.trim().isEmpty()) {
                System.err.println("DEBUG JSP: errorOutput is empty or whitespace for objectPath '" + objectPath + "' (Length: " + errorOutput.length() + ")");
            } else {
                System.err.println("--- JSP: Python script error output for '" + objectPath + "' (Length: " + errorOutput.length() + "):\n--BEGIN PYTHON STDERR--\n" + errorOutput + "--END PYTHON STDERR--");
            }

            if (exitCode == 0 && output.length() > 0) {
                System.out.println("--- JSP: Python script success, output: " + output.toString().trim());
                return output.toString().trim();
            } else {
                System.err.println("--- JSP: Failed to generate signed URL via Python for '" + objectPath + "'. Exit code: " + exitCode + ". Python stdout: '" + output.toString() + "'");
                return ""; // Return empty for failure
            }

        } catch (IOException | InterruptedException e) {
            System.err.println("--- JSP: Exception while executing Python script for '" + objectPath + "': " + e.getMessage());
            e.printStackTrace(System.err);
            return "";
        } finally {
            if (reader != null) { try { reader.close(); } catch (IOException e) { /* ignore */ } }
            if (process != null) {
                try { if (process.getInputStream() != null) process.getInputStream().close(); } catch (IOException e) {/*ignore*/}
                try { if (process.getErrorStream() != null) process.getErrorStream().close(); } catch (IOException e) {/*ignore*/}
                try { if (process.getOutputStream() != null) process.getOutputStream().close(); } catch (IOException e) {/*ignore*/}
                process.destroy(); 
            }
        }
    }

    // Helper for JSON string escaping
    private String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\").replace("\"", "\\\"").replace("\b", "\\b")
                  .replace("\f", "\\f").replace("\n", "\\n").replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
%>
<sql:setDataSource var="dbSource" dataSource="jdbc/CustomOscarDB" />
<%
    // Main scriptlet logic for fetching and formatting appointment data
    String demographicNoStr = request.getParameter("demographic_no");
    StringBuilder jsonOutput = new StringBuilder("[");
    SimpleDateFormat sdfDateOutput = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat sdfTimeOutput = new SimpleDateFormat("HH:mm");

    if (demographicNoStr != null && !demographicNoStr.trim().isEmpty()) {
        System.out.println("--- JSP: Processing for demographic_no: " + demographicNoStr);
        try {
            Integer.parseInt(demographicNoStr); // Validate if demographicNoStr is a number, good practice
%> 
            <sql:query var="appointmentData" dataSource="${dbSource}">
                SELECT 
                    a.appointment_no,
                    a.appointment_date,
                    a.start_time,
                    a.end_time,
                    a.type,
                    a.reason,
                    a.location,
                    a.notes,
                    p.first_name AS provider_first_name,
                    p.last_name AS provider_last_name,
                    ari.s3_object_key 
                FROM appointment a
                LEFT JOIN provider p ON a.provider_no = p.provider_no
                LEFT JOIN appointment_reason_images ari ON a.appointment_no = ari.appointment_no_fk
                WHERE a.demographic_no = ? 
                  AND a.appointment_date >= CURDATE()
                  AND (a.status IS NULL OR a.status = 't' OR a.status = '')
                ORDER BY a.appointment_date ASC, a.start_time ASC, a.appointment_no ASC, ari.created_at ASC;
                <sql:param value="${param.demographic_no}" />
            </sql:query>
<%
            javax.servlet.jsp.jstl.sql.Result appointmentDataResult = 
                (javax.servlet.jsp.jstl.sql.Result) pageContext.findAttribute("appointmentData");

            if (appointmentDataResult != null && appointmentDataResult.getRowCount() > 0) {
                Map<String, Map<String, Object>> appointmentsMap = new LinkedHashMap<>();
                boolean firstAppointmentProcessedOuter = true;

                for (Object[] row : appointmentDataResult.getRowsByIndex()) {
                    String appointmentNo = (row[0] != null) ? row[0].toString() : null;
                    if (appointmentNo == null) continue;

                    if (!appointmentsMap.containsKey(appointmentNo)) {
                        Map<String, Object> apptDetails = new LinkedHashMap<>();
                        apptDetails.put("type", (row[4] != null) ? escapeJson(row[4].toString()) : "");
                        System.out.println("--- JSP DEBUG: Appointment " + appointmentNo + " type: " + ((row[4] != null) ? row[4].toString() : "NULL"));
                        apptDetails.put("appointment_no", escapeJson(appointmentNo));
                        apptDetails.put("appointment_date", (row[1] instanceof java.util.Date) ? sdfDateOutput.format((java.util.Date)row[1]) : "");
                        apptDetails.put("start_time", (row[2] instanceof java.util.Date) ? sdfTimeOutput.format((java.util.Date)row[2]) : "");
                        apptDetails.put("end_time", (row[3] instanceof java.util.Date) ? sdfTimeOutput.format((java.util.Date)row[3]) : "");
                        apptDetails.put("reason", (row[5] != null) ? escapeJson(row[5].toString()) : "");
                        apptDetails.put("location", (row[6] != null) ? escapeJson(row[6].toString()) : "");
                        apptDetails.put("notes", (row[7] != null) ? escapeJson(row[7].toString()) : "");
                        String providerFirstName = (row[8] != null) ? row[8].toString() : "";
                        String providerLastName = (row[9] != null) ? row[9].toString() : "";
                        String providerFullName = (providerFirstName + " " + providerLastName).trim();
                        apptDetails.put("provider_name", escapeJson(providerFullName.isEmpty() ? "N/A" : providerFullName));
                        apptDetails.put("images", new ArrayList<String>());
                        appointmentsMap.put(appointmentNo, apptDetails);
                    }

                    if (row[10] != null) { 
                        String s3ObjectKey = row[10].toString();
                        String signedImageUrl = generateSupabaseSignedUrlViaPython(s3ObjectKey);
                        
                        if (signedImageUrl != null && !signedImageUrl.isEmpty()) {
                            @SuppressWarnings("unchecked")
                            List<String> imagesList = (List<String>) appointmentsMap.get(appointmentNo).get("images");
                            imagesList.add(escapeJson(signedImageUrl)); 
                        } else {
                            System.out.println("--- JSP INFO: Failed to generate Supabase pre-signed URL (returned empty/null) for S3 object key (via Python): " + s3ObjectKey + " (Appointment No: " + appointmentNo + ")");
                        }
                    }
                }
                
                for (Map.Entry<String, Map<String, Object>> entry : appointmentsMap.entrySet()) {
                    if (!firstAppointmentProcessedOuter) {
                        jsonOutput.append(",");
                    }
                    Map<String, Object> apptDetails = entry.getValue();
                    jsonOutput.append("{");
                    boolean firstDetailProcessed = true;
                    for (Map.Entry<String, Object> detailEntry : apptDetails.entrySet()) {
                        if (!firstDetailProcessed) {
                            jsonOutput.append(",");
                        }
                        jsonOutput.append("\"").append(escapeJson(detailEntry.getKey())).append("\":");
                        Object value = detailEntry.getValue();
                        if (value instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<String> imagesList = (List<String>) value;
                            jsonOutput.append("[");
                            boolean firstImageProcessed = true;
                            for (String imageUrl : imagesList) {
                                if (!firstImageProcessed) {
                                    jsonOutput.append(",");
                                }
                                jsonOutput.append("\"").append(imageUrl).append("\""); // imageUrl is already escaped if it came from escapeJson(signedImageUrl)
                                firstImageProcessed = false;
                            }
                            jsonOutput.append("]");
                        } else if (value instanceof String) {
                            jsonOutput.append("\"").append(value.toString()).append("\""); // Assumes other strings were already escaped if needed
                        } else {
                             jsonOutput.append(value.toString()); 
                        }
                        firstDetailProcessed = false;
                    }
                    jsonOutput.append("}");
                    firstAppointmentProcessedOuter = false;
                }
            } else {
                System.out.println("--- JSP: No appointment data found for demographic_no: " + demographicNoStr);
            }
        } catch (NumberFormatException nfe) {
            System.err.println("--- JSP: Invalid demographic_no parameter (not a number): " + demographicNoStr + ". Error: " + nfe.getMessage());
            jsonOutput.setLength(0); jsonOutput.append("[]"); // Clear and set to empty array
        } catch (Exception e) {
            System.err.println("--- JSP: Error processing appointments for demographic_no " + demographicNoStr + ": " + e.getMessage());
            e.printStackTrace(System.err);
            jsonOutput.setLength(0); jsonOutput.append("[]");
        }
    } else {
        System.out.println("--- JSP: demographic_no parameter is missing or empty.");
    }

    jsonOutput.append("]");
    out.print(jsonOutput.toString());
    System.out.println("--- JSP: Final JSON output: " + jsonOutput.toString());
%>