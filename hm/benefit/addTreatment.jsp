<%@ page import="java.sql.*, java.util.*, java.io.*" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!-- Include authentication check -->
<%@ include file="../includes/hmAuth.jsp" %>

<%
    // Set page title for header.jsp
    request.setAttribute("pageTitle", "治疗项目管理");
%>

<!-- Database connection setup -->
<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<%
    String action = request.getParameter("action");
    String treatmentFormToken = null; // Will hold the token to be used in the form's hidden field
    String sessionTokenForCheck = (String)session.getAttribute("treatmentFormToken"); // Token currently in session to check against

    // Determine if this is a request that should display the form with a fresh token, 
    // or a submission that should use an existing token.
    boolean isSubmissionAction = "submit".equals(action) || "deactivate".equals(action) || "activate".equals(action);

    if (!isSubmissionAction) { // Page load for display (action is null, or not a submit/deactivate/activate)
        treatmentFormToken = UUID.randomUUID().toString();
        session.setAttribute("treatmentFormToken", treatmentFormToken);
        sessionTokenForCheck = treatmentFormToken; // For this request, the session token *is* the newly generated one
        System.out.println("DEBUG CSRF: New token generated for form display/load: " + treatmentFormToken);
    } else { // This is a submission action
        // The form should have submitted a token. We check it against sessionTokenForCheck.
        // treatmentFormToken for the hidden input (if the page re-renders with the form, e.g. on error) 
        // should ideally be the sessionTokenForCheck if it's still valid, or a new one if the submission failed due to token and we want to allow retry.
        // For simplicity on error display, we can re-issue a new token if the current one failed.
        // However, the initial check must use sessionTokenForCheck.
        treatmentFormToken = sessionTokenForCheck; // This is for potentially re-rendering the form value
        if (treatmentFormToken == null) {
             System.err.println("DEBUG CSRF: sessionTokenForCheck is NULL during a submission action: " + action + ". This means no token was in session.");
             // If no token in session during submit, it will fail. 
             // We might generate a new one here if we re-render the form to allow a new attempt.
             treatmentFormToken = UUID.randomUUID().toString(); // Generate a new one for the form if it re-renders after error
             // DONT set it in session here, as the check below needs to fail against the (null) sessionTokenForCheck
        }
    }

    String treatmentName = request.getParameter("treatmentName");
    String sessionDuration = request.getParameter("sessionDuration");
    String pricePerSession = request.getParameter("pricePerSession");
    String treatmentId = request.getParameter("treatmentId");
    boolean isSuccess = false;
    String message = "";

    String requestToken = request.getParameter("formToken");
    
    System.out.println("DEBUG CSRF: action = " + action);
    System.out.println("DEBUG CSRF: sessionTokenForCheck (from session, for checking) = " + sessionTokenForCheck);
    System.out.println("DEBUG CSRF: requestToken (from form submission) = " + requestToken);
    System.out.println("DEBUG CSRF: treatmentFormToken (value for form hidden input if re-rendered) = " + treatmentFormToken);

    if (isSubmissionAction) {
        if (sessionTokenForCheck == null || !sessionTokenForCheck.equals(requestToken)) {
            message = "安全验证失败，请重试。";
            System.err.println("CSRF TOKEN CHECK FAILED: sessionTokenForCheck=" + sessionTokenForCheck + ", requestToken=" + requestToken);
            // If token check fails, a new token should be available in treatmentFormToken for the re-rendered form
            // If sessionTokenForCheck was null, we already made a new treatmentFormToken.
            // If they didn't match, also make a new one for the form to use on next attempt.
            treatmentFormToken = UUID.randomUUID().toString();
            session.setAttribute("treatmentFormToken", treatmentFormToken); // Store the new one for the *next* attempt
            System.out.println("DEBUG CSRF: Token check failed. Issued new token for form re-render: " + treatmentFormToken);
        } else {
            // Valid token for a submission action
            message = ""; // Clear any previous message if token is now valid
            session.removeAttribute("treatmentFormToken"); // Consume the token
            System.out.println("DEBUG CSRF: Token matched and removed for action: " + action);
            
            // Handle Activation and Deactivation
            if ("deactivate".equals(action) && treatmentId != null) {
%>
                <sql:update dataSource="${snapshot}">
                    UPDATE FM_Treatments SET active = 0 WHERE treatment_id = ?
                    <sql:param value="<%=treatmentId%>" />
                </sql:update>
                <script>
                    alert("Treatment Deactivated Successfully!");
                    window.location.href = "addTreatment.jsp";
                </script>
<%
            } else if ("activate".equals(action) && treatmentId != null) {
%>
                <sql:update dataSource="${snapshot}">
                    UPDATE FM_Treatments SET active = 1 WHERE treatment_id = ?
                    <sql:param value="<%=treatmentId%>" />
                </sql:update>
                <script>
                    alert("Treatment Activated Successfully!");
                    window.location.href = "addTreatment.jsp";
                </script>
<%
            }

            if ("submit".equals(action)) {
                if (treatmentName != null && sessionDuration != null && pricePerSession != null &&
                    !treatmentName.isEmpty() && !sessionDuration.isEmpty() && !pricePerSession.isEmpty()) {

                    try {
                        // Convert sessionDuration and pricePerSession to numeric types
                        int duration = Integer.parseInt(sessionDuration);
                        double price = Double.parseDouble(pricePerSession);

                        // Insert the new treatment into the FM_Treatments table
%>
                        <sql:update dataSource="${snapshot}">
                            INSERT INTO FM_Treatments (treatment_name, session_duration, price_per_session, active) 
                            VALUES (?, ?, ?, 1)
                            <sql:param value="<%=treatmentName%>" />
                            <sql:param value="<%=duration%>" />
                            <sql:param value="<%=price%>" />
                        </sql:update>

                        <%
                        isSuccess = true;
                        message = "New Treatment Added Successfully!";
                    } catch (Exception e) {
                        e.printStackTrace();
                        message = "Failed to add the treatment. Error: " + e.getMessage();
                    }
                } else {
                    message = "All fields are required!";
                }
            }
        }
    }
%>

<!-- Query to get all treatments -->
<sql:query dataSource="${snapshot}" var="treatmentList">
    SELECT * FROM FM_Treatments ORDER BY treatment_id DESC;
</sql:query>

<%@ include file="../includes/header.jsp" %>

<style>
    .treatment-container {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
    }
    .btn-action {
        margin-right: 5px;
    }
    /* Fix the CSS for the sidebar and main content */
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        width: 16.666667%;
        z-index: 100;
        background: linear-gradient(180deg, #3b82f6 0%, #1e40af 100%);
        overflow-y: auto;
    }
    .main-content {
        margin-left: 16.666667%;
        width: 83.333333%;
    }
    .sidebar-link {
        color: white;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        padding: 12px 15px;
        text-decoration: none;
    }
    .sidebar-link:hover, .sidebar-link.active {
        background-color: rgba(255,255,255,0.15);
        color: white;
        text-decoration: none;
    }
    .sidebar-link i {
        margin-right: 10px;
        font-size: 1.25rem;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 - 直接内嵌而不是include -->
        <div class="sidebar">
            <div class="p-4 text-white">
                <h3 class="fw-bold mb-4">健康管理系统</h3>
                <div class="mb-4">
                    <small class="d-block text-white-50">会员管理</small>
                    <hr class="bg-white opacity-25 my-2">
                </div>
                <a href="../hmMenu.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/hmMenu.jsp' ? 'active' : ''}">
                    <i class="bi bi-search"></i> 会员搜索
                </a>
                <a href="../hmDashboard.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/hmDashboard.jsp' ? 'active' : ''}">
                    <i class="bi bi-speedometer2"></i> 仪表盘
                </a>
                <a href="../members.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/members.jsp' ? 'active' : ''}">
                    <i class="bi bi-people-fill"></i> 会员列表
                </a>
                <a href="../renewals.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/renewals.jsp' ? 'active' : ''}">
                    <i class="bi bi-arrow-repeat"></i> 续费情况
                </a>
                <a href="../membersNew.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/membersNew.jsp' ? 'active' : ''}">
                    <i class="bi bi-people"></i> 家庭会员
                </a>
                <a href="../priexam.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/priexam.jsp' ? 'active' : ''}">
                    <i class="bi bi-clipboard-data"></i> 自费项目统计
                </a>

                <!-- 治疗套餐管理系统 -->
                <div class="mb-4 mt-4">
                    <small class="d-block text-white-50">治疗套餐管理</small>
                    <hr class="bg-white opacity-25 my-2">
                </div>
                <a href="addTreatment.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addTreatment.jsp' ? 'active' : ''}">
                    <i class="bi bi-bandaid"></i> 治疗项目管理
                </a>
                <a href="addPackage.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addPackage.jsp' ? 'active' : ''}">
                    <i class="bi bi-box"></i> 治疗套餐管理
                </a>
                <a href="addInvoice.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addInvoice.jsp' ? 'active' : ''}">
                    <i class="bi bi-cart-plus"></i> 套餐销售
                </a>
                <a href="addRedemptions.jsp" class="sidebar-link ${pageContext.request.servletPath eq '/hm/benefit/addRedemptions.jsp' ? 'active' : ''}">
                    <i class="bi bi-check-circle"></i> 治疗兑换管理
                </a>
                
                <div class="mb-4 mt-4">
                    <small class="d-block text-white-50">系统</small>
                    <hr class="bg-white opacity-25 my-2">
                </div>
                <a href="https://oscar.mmcwellness.ca/oscar/provider/providercontrol.jsp" class="sidebar-link">
                    <i class="bi bi-box-arrow-left"></i> 返回 Oscar
                </a>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">治疗项目管理</h2>
                <span class="text-muted">添加和管理治疗项目</span>
            </div>
            
            <div class="treatment-container">
                <!-- Display error messages -->
                <% if (!message.isEmpty()) { %>
                    <% if (isSuccess) { %>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>成功!</strong> <%= message %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <% } else { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <strong>错误!</strong> <%= message %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <% } %>
                <% } %>
                
                <!-- Treatment Form -->
                <form method="get">
                    <div class="mb-3">
                        <label for="treatmentName" class="form-label">治疗项目名称</label>
                        <input type="text" class="form-control" id="treatmentName" name="treatmentName" required>
                    </div>
                    <div class="mb-3">
                        <label for="sessionDuration" class="form-label">单次治疗时长 (分钟)</label>
                        <input type="number" class="form-control" id="sessionDuration" name="sessionDuration" required>
                    </div>
                    <div class="mb-3">
                        <label for="pricePerSession" class="form-label">单次治疗价格 ($)</label>
                        <input type="number" step="0.01" class="form-control" id="pricePerSession" name="pricePerSession" required>
                    </div>
                    <input type="hidden" name="formToken" value="<%=treatmentFormToken%>">
                    <button type="submit" class="btn btn-primary w-100" name="action" value="submit">提交</button>
                </form>

                <hr>

                <!-- Display Treatment List -->
                <h3 class="mt-4">已有治疗项目</h3>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped mt-3">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>治疗项目名称</th>
                                <th>单次时长 (分钟)</th>
                                <th>单次价格 ($)</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="treatment" items="${treatmentList.rows}">
                                <tr>
                                    <td>${treatment.treatment_id}</td>
                                    <td>${treatment.treatment_name}</td>
                                    <td>${treatment.session_duration}</td>
                                    <td>${treatment.price_per_session}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${treatment.active}">
                                                <span class="badge bg-success">有效</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge bg-danger">已禁用</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${treatment.active}">
                                                <!-- Deactivate Button -->
                                                <form method="get" style="display:inline;">
                                                    <input type="hidden" name="treatmentId" value="${treatment.treatment_id}" />
                                                    <input type="hidden" name="formToken" value="<%=treatmentFormToken%>">
                                                    <button type="submit" name="action" value="deactivate" class="btn btn-sm btn-danger btn-action">
                                                        禁用
                                                    </button>
                                                </form>
                                            </c:when>
                                            <c:otherwise>
                                                <!-- Activate Button -->
                                                <form method="get" style="display:inline;">
                                                    <input type="hidden" name="treatmentId" value="${treatment.treatment_id}" />
                                                    <input type="hidden" name="formToken" value="<%=treatmentFormToken%>">
                                                    <button type="submit" name="action" value="activate" class="btn btn-sm btn-success btn-action">
                                                        启用
                                                    </button>
                                                </form>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include file="../includes/footer.jsp" %>
