<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/sql" prefix="sql"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>

<!-- Include authentication check -->
<%@ include file="includes/hmAuth.jsp" %>

<%
    // Set page title for header.jsp
    request.setAttribute("pageTitle", "会员搜索");

    String searchTerm = request.getParameter("searchTerm");
    if (searchTerm == null) {
        searchTerm = "";
    }
    pageContext.setAttribute("searchTerm", searchTerm);
%>

<sql:setDataSource var="snapshot" dataSource="jdbc/CustomOscarDB" />

<c:if test="${not empty searchTerm}">
    <sql:query dataSource="${snapshot}" var="searchResults">
        SELECT
            d.demographic_no,
            d.first_name,
            d.last_name,
            DATE_FORMAT(d.date_of_birth, '%Y-%m-%d') AS date_of_birth,
            d.email,
            d.phone,
            p.provider_no AS main_provider_no,
            CONCAT(p.first_name, ' ', p.last_name) AS main_provider_name
        FROM demographic d
        LEFT JOIN provider p ON d.provider_no = p.provider_no
        WHERE
            CAST(d.demographic_no AS CHAR) LIKE ? OR
            d.first_name LIKE ? OR
            d.last_name LIKE ? OR
            CONCAT(d.first_name, ' ', d.last_name) LIKE ? OR
            CONCAT(d.last_name, ' ', d.first_name) LIKE ? OR
            d.email LIKE ? OR
            d.phone LIKE ? OR
            d.hin LIKE ?
        ORDER BY d.last_name, d.first_name;
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
        <sql:param value="%${searchTerm}%"/>
    </sql:query>
</c:if>

<%@ include file="includes/header.jsp" %>

<style>
    .search-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 2rem;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .search-input {
        height: 50px;
        font-size: 1.2rem;
    }
    .search-button {
        height: 50px;
    }
    .results-table th {
        background-color: #f8f9fa;
    }
    .modal-lg {
        max-width: 70%;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- 侧边导航栏 -->
        <%@ include file="includes/sidebar.jsp" %>

        <!-- 主内容区 -->
        <div class="main-content p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">会员搜索</h2>
                <div class="d-flex align-items-center gap-3">
                    <span class="text-muted">当前日期: <%= today %></span>
                    <a href="hmMenu.jsp?logout=true" class="btn btn-sm btn-outline-danger">
                        <i class="bi bi-box-arrow-right"></i> 退出
                    </a>
                </div>
            </div>

            <div class="search-container">
                <form method="get" action="hmMenu.jsp" class="mb-4">
                    <div class="input-group input-group-lg mb-3">
                        <input type="text" name="searchTerm" class="form-control search-input"
                               placeholder="输入姓名、生日 (YYYY-MM-DD)、手机、档案号或邮箱搜索..." \
                               value="<c:out value='${fn:escapeXml(searchTerm)}'/>"
                               aria-label="Search Term">
                        <button class="btn btn-primary search-button" type="submit">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                    </div>
                </form>

                <c:if test="${not empty searchResults && searchResults.rowCount > 0}">
                    <h4 class="mb-3">搜索结果 (<c:out value="${searchResults.rowCount}"/> 条)</h4>
                    <div class="table-responsive">
                        <table class="table table-hover results-table">
                            <thead>
                                <tr>
                                    <th>档案号</th>
                                    <th>姓名</th>
                                    <th>出生日期</th>
                                    <th>邮箱</th>
                                    <th>电话</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="patient" items="${searchResults.rows}">
                                    <tr>
                                        <td><c:out value="${patient.demographic_no}"/></td>
                                        <td><c:out value="${patient.last_name}, ${patient.first_name}"/></td>
                                        <td><c:out value="${patient.date_of_birth}"/></td>
                                        <td><c:out value="${patient.email}"/></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${not empty patient.cell_phone}"><c:out value="${patient.cell_phone}"/> (手机)</c:when>
                                                <c:when test="${not empty patient.phone}"><c:out value="${patient.phone}"/> (住宅)</c:when>
                                                <c:otherwise>N/A</c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <a href="patientDetails.jsp?demographic_no=${patient.demographic_no}"
                                               class="btn btn-sm btn-outline-primary view-details-btn">
                                                <i class="bi bi-person-lines-fill"></i> 查看详情
                                            </a>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </c:if>
                <c:if test="${not empty searchTerm && (empty searchResults || searchResults.rowCount == 0)}">
                    <div class="alert alert-warning" role="alert">
                        没有找到与 "<c:out value='${fn:escapeXml(searchTerm)}'/>" 相关的会员。
                    </div>
                </c:if>
            </div>
        </div>
    </div>
</div>

<%@ include file="includes/footer.jsp" %>
