<%@ page import="java.sql.*, org.json.simple.JSONObject, org.oscarehr.util.DBUtils, org.oscarehr.util.StringUtil" %>

<!-- API authentication check -->
<%
    // Check if user is logged into Oscar first
    String loggedInUser = (String) session.getAttribute("user");
    if (loggedInUser == null || loggedInUser.trim().isEmpty()) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Not logged into Oscar system. Please login first.\"}");
        return;
    }

    // API authentication check for HM module
    String authenticated = (String) session.getAttribute("hmMenuAuthenticated");
    if (!"true".equals(authenticated)) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"error\":\"Unauthorized access to HM system. Please login to HM module first.\"}");
        return;
    }
%>
<%@ page contentType="application/json; charset=UTF-8" %>
<%
    JSONObject jsonResponse = new JSONObject();
    Connection conn = null;
    PreparedStatement pstmt = null;
    ResultSet rs = null;

    String demoNoStr = request.getParameter("demographic_no");

    if (StringUtil.isNullOrEmpty(demoNoStr)) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        jsonResponse.put("error", "Missing demographic_no parameter.");
        out.print(jsonResponse.toJSONString());
        return;
    }

    int demographicNo = 0;
    try {
        demographicNo = Integer.parseInt(demoNoStr);
    } catch (NumberFormatException e) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        jsonResponse.put("error", "Invalid demographic_no parameter.");
        out.print(jsonResponse.toJSONString());
        return;
    }

    try {
        conn = DBUtils.getCustomConnection(); // Using CustomOscarDB by default from DBUtils
        if (conn == null) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            jsonResponse.put("error", "Could not establish database connection.");
            out.print(jsonResponse.toJSONString());
            return;
        }

        String sql = "SELECT d.first_name, d.last_name, d.email, d.phone, " +
                     "d.address, d.city, d.province, d.postal, d.avatar_url, " +
                     "DATE_FORMAT(d.date_of_birth, '%Y-%m-%d') AS date_of_birth, " +
                     "p.provider_no AS main_provider_no, CONCAT(p.first_name, ' ', p.last_name) AS main_provider_name " +
                     "FROM demographic d " +
                     "LEFT JOIN provider p ON d.provider_no = p.provider_no " +
                     "WHERE d.demographic_no = ?";

        pstmt = conn.prepareStatement(sql);
        pstmt.setInt(1, demographicNo);
        rs = pstmt.executeQuery();

        if (rs.next()) {
            JSONObject patientData = new JSONObject();
            patientData.put("demographic_no", demographicNo);
            patientData.put("first_name", StringUtil.showNull(rs.getString("first_name")));
            patientData.put("last_name", StringUtil.showNull(rs.getString("last_name")));
            patientData.put("email", StringUtil.showNull(rs.getString("email")));
            patientData.put("phone", StringUtil.showNull(rs.getString("phone")));
            patientData.put("address", StringUtil.showNull(rs.getString("address")));
            patientData.put("city", StringUtil.showNull(rs.getString("city")));
            patientData.put("province", StringUtil.showNull(rs.getString("province")));
            patientData.put("postal", StringUtil.showNull(rs.getString("postal")));
            patientData.put("avatar_url", StringUtil.showNull(rs.getString("avatar_url")));
            patientData.put("date_of_birth", StringUtil.showNull(rs.getString("date_of_birth")));
            patientData.put("main_provider_name", StringUtil.showNull(rs.getString("main_provider_name")));
            jsonResponse.put("patient", patientData);
        } else {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            jsonResponse.put("error", "Patient not found.");
        }

    } catch (Exception e) {
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        jsonResponse.put("error", "Error fetching patient details: " + e.getMessage());
        // Consider logging the exception e.printStackTrace();
    } finally {
        try { if (rs != null) rs.close(); } catch (SQLException e) { /* ignored */ }
        try { if (pstmt != null) pstmt.close(); } catch (SQLException e) { /* ignored */ }
        try { if (conn != null) conn.close(); } catch (SQLException e) { /* ignored */ }
    }
    out.print(jsonResponse.toJSONString());
    out.flush();
%>