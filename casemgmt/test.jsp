<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>

<style>
#Appointment\ Photos {
    max-width: 350px !important;
    width: 100% !important;
}

#Appointment\ Photos h3 {
    background-color: #4a7c59 !important;
    color: white !important;
    margin: 0 !important;
    padding: 8px !important;
    font-weight: bold !important;
}
#Appointment\ Photos ul {
    background-color: #f0f8f0 !important;
    margin: 0 !important;
    padding: 5px !important;
    border-radius: 8px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}
#Appointment\ Photos li {
    background-color: transparent !important;
    padding: 3px 8px !important;
    border-bottom: 1px solid #e0e8e0 !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    white-space: normal !important;
    overflow-wrap: break-word !important;
    line-height: 1.4 !important;
}
#Appointment\ Photos li:last-child {
    border-bottom: none !important;
}
#Appointment\ Photos li a {
    color: #2d5016 !important;
    text-decoration: none !important;
    display: block !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    white-space: normal !important;
    overflow-wrap: break-word !important;
}
#Appointment\ Photos li a:hover {
    color: #4a7c59 !important;
    text-decoration: underline !important;
}

.loading-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.no-appointments {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* Container width constraint */
#appointmentsList {
    max-width: 100% !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
}
</style>

<h3>Appointment Photos</h3>

<div id="appointmentsList">
    <div class="loading-message">Loading appointments...</div>
</div>

<script>
(function() {
    'use strict';
    
    // Global function for getting patient ID
    window.getPatientId = function() {
        var demoNo = null;
        
        // Try current URL parameters
        try {
            var urlParams = new URLSearchParams(window.location.search);
            demoNo = urlParams.get('demographicNo');
            if (demoNo) {
                return demoNo;
            }
        } catch (e) {
            // Continue to next method
        }
        
        // Try parent window URL parameters
        try {
            if (window.parent && window.parent.location) {
                var parentParams = new URLSearchParams(window.parent.location.search);
                demoNo = parentParams.get('demographicNo');
                if (demoNo) {
                    return demoNo;
                }
            }
        } catch (e) {
            // Continue to fallback
        }
        
        // Try to get from document/page context
        try {
            // Look for demographic_no in various possible places
            var demoFromElement = document.querySelector('input[name="demographic_no"]');
            if (demoFromElement && demoFromElement.value) {
                return demoFromElement.value;
            }
            
            // Look for any element with data-demographic-no attribute
            var elementWithDemo = document.querySelector('[data-demographic-no]');
            if (elementWithDemo) {
                return elementWithDemo.getAttribute('data-demographic-no');
            }
            
            // Check if demographicNo is defined globally
            if (typeof demographicNo !== 'undefined' && demographicNo) {
                return demographicNo;
            }
        } catch (e) {
            // Continue to next fallback
        }
        
        // Fallback: return null so we can show an error instead of wrong patient
        return null;
    };
    
    function loadAppointments() {
        var demographicNo = getPatientId();
        var container = document.getElementById('appointmentsList');
        
        if (!demographicNo) {
            container.innerHTML = '<div class="no-appointments">Patient ID not found</div>';
            return;
        }
        
        var contextPath = '<%=request.getContextPath()%>';
        var url = contextPath + '/hm/getPatientAppointments.jsp?demographic_no=' + demographicNo;
        
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        var responseText = xhr.responseText.trim();
                        if (!responseText) {
                            container.innerHTML = '<div class="no-appointments">No appointments found</div>';
                            return;
                        }
                        
                        var appointments = JSON.parse(responseText);
                        displayAppointments(appointments);
                    } catch (e) {
                        container.innerHTML = '<div class="no-appointments">Error loading appointments</div>';
                    }
                } else {
                    container.innerHTML = '<div class="no-appointments">Error loading appointments</div>';
                }
            }
        };
        
        xhr.onerror = function() {
            container.innerHTML = '<div class="no-appointments">Network error</div>';
        };
        
        xhr.timeout = 15000;
        xhr.ontimeout = function() {
            container.innerHTML = '<div class="no-appointments">Request timeout</div>';
        };
        
        xhr.send();
    }
    
    function displayAppointments(appointments) {
        var container = document.getElementById('appointmentsList');
        
        if (!appointments || appointments.length === 0) {
            container.innerHTML = '<div class="no-appointments">No upcoming appointments found</div>';
            return;
        }
        
        var html = '<ul>';
        for (var i = 0; i < appointments.length; i++) {
            var apt = appointments[i];
            var displayText = formatAppointmentText(apt);
            html += '<li><a href="javascript:void(0)" onclick="window.openAppointmentDetails(\'' + apt.appointment_no + '\')">' + displayText + '</a></li>';
        }
        html += '</ul>';
        
        container.innerHTML = html;
        
        var hiddenInput = document.getElementById('Appointment Photosnum');
        if (hiddenInput) {
            hiddenInput.value = appointments.length;
        }
    }
    
    function formatAppointmentText(appointment) {
        var parts = [];
        
        
        if (appointment.appointment_date) {
            var dateStr = appointment.appointment_date;
            if (appointment.start_time) {
                dateStr += ' (' + appointment.start_time;
                dateStr += ')';
            }
            parts.push(dateStr);
        }
        
        if (appointment.reason) {
            parts.push(appointment.reason);
        }
        if (appointment.provider_name) {
            parts.push(appointment.provider_name);
        }
        
        return parts.join(' - ');
    }
    
    // Global function for opening appointment details
    window.openAppointmentDetails = function(appointmentId) {
        var demographicNo = getPatientId();
        
        if (!demographicNo) {
            alert('Patient ID not found');
            return;
        }
        
        var baseUrl = '<%=request.getContextPath()%>/casemgmt/appointmentPhotos.jsp';
        var url = baseUrl + '?demographicNo=' + demographicNo + '&focus=' + appointmentId;
        
        if (typeof popupPage === 'function') {
            popupPage(700, 900, 'appointmentPhotos', url);
        } else {
            window.open(url, 'appointmentPhotos', 'width=700,height=900,scrollbars=yes,resizable=yes');
        }
    };
    
    // Auto-load when page is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(loadAppointments, 500);
        });
    } else {
        setTimeout(loadAppointments, 500);
    }
    
})();
</script>

<input type="hidden" id="Appointment Photosnum" value="0">

<style>
#Family\ Nutritionist\ Billing {
    max-width: 350px !important;
    width: 100% !important;
    margin-top: 20px !important;
}

#Family\ Nutritionist\ Billing h3 {
    background-color: #7c4a59 !important;
    color: white !important;
    margin: 0 !important;
    padding: 8px !important;
    font-weight: bold !important;
}

#Family\ Nutritionist\ Billing .family-member {
    background-color: #f8f0f0 !important;
    margin: 0 !important;
    padding: 8px !important;
    border-bottom: 1px solid #e8e0e0 !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    white-space: normal !important;
    overflow-wrap: break-word !important;
    line-height: 1.4 !important;
}

#Family\ Nutritionist\ Billing .family-member:last-child {
    border-bottom: none !important;
}

#Family\ Nutritionist\ Billing .member-name {
    font-weight: bold !important;
    color: #2d1a20 !important;
}

#Family\ Nutritionist\ Billing .member-relationship {
    color: #666 !important;
    font-size: 0.9em !important;
    font-style: italic !important;
}

#Family\ Nutritionist\ Billing .billing-status {
    margin-top: 3px !important;
}

#Family\ Nutritionist\ Billing .billing-yes {
    color: #28a745 !important;
    font-weight: bold !important;
}

#Family\ Nutritionist\ Billing .billing-no {
    color: #dc3545 !important;
}

#Family\ Nutritionist\ Billing .loading-message,
#Family\ Nutritionist\ Billing .no-data {
    text-align: center !important;
    padding: 20px !important;
    color: #666 !important;
    font-style: italic !important;
}
</style>

<div id="Family Nutritionist Billing">
    <h3>Family Nutritionist Billing</h3>
    <div id="familyBillingList">
        <div class="loading-message">Loading family billing information...</div>
    </div>
</div>

<script>
(function() {
    'use strict';
    
    function loadFamilyNutritionistBilling() {
        var demographicNo = getPatientId();
        var container = document.getElementById('familyBillingList');
        
        if (!demographicNo) {
            container.innerHTML = '<div class="no-data">Patient ID not found</div>';
            return;
        }
        
        var contextPath = '<%=request.getContextPath()%>';
        var url = contextPath + '/hm/getFamilyNutritionistBilling.jsp?demographic_no=' + demographicNo;
        
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        var responseText = xhr.responseText.trim();
                        if (!responseText) {
                            container.innerHTML = '<div class="no-data">No family information found</div>';
                            return;
                        }
                        
                        var familyMembers = JSON.parse(responseText);
                        displayFamilyBilling(familyMembers);
                    } catch (e) {
                        container.innerHTML = '<div class="no-data">Error loading family billing information</div>';
                    }
                } else {
                    container.innerHTML = '<div class="no-data">Error loading family billing information</div>';
                }
            }
        };
        
        xhr.onerror = function() {
            container.innerHTML = '<div class="no-data">Network error</div>';
        };
        
        xhr.timeout = 15000;
        xhr.ontimeout = function() {
            container.innerHTML = '<div class="no-data">Request timeout</div>';
        };
        
        xhr.send();
    }
    
    function displayFamilyBilling(familyMembers) {
        var container = document.getElementById('familyBillingList');
        
        if (!familyMembers || familyMembers.length === 0) {
            container.innerHTML = '<div class="no-data">No family members found</div>';
            return;
        }
        
        var html = '';
        for (var i = 0; i < familyMembers.length; i++) {
            var member = familyMembers[i];
            var relationshipText = member.relationship || 'Unknown relation';
            var statusClass = member.has_nutritionist_billing === 'YES' ? 'billing-yes' : 'billing-no';
            var statusText = member.has_nutritionist_billing === 'YES' ? 'Has AHM_NCN' : 'No AHM_NCN';
            
            html += '<div class="family-member">';
            html += '<div class="member-name">' + (member.full_name || 'Unknown') + '</div>';
            html += '<div class="member-relationship">' + relationshipText + '</div>';
            html += '<div class="billing-status"><span class="' + statusClass + '">' + statusText + '</span></div>';
            html += '</div>';
        }
        
        container.innerHTML = html;
    }
    
    // Load family billing information when page is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(loadFamilyNutritionistBilling, 600);
        });
    } else {
        setTimeout(loadFamilyNutritionistBilling, 600);
    }
    
})();
</script> 