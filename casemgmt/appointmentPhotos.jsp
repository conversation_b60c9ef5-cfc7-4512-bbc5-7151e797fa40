<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <title>Appointment Photos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .appointment-container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .appointment-header {
            border-bottom: 2px solid #4a7c59;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .appointment-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 0 0 5px 0;
        }
        
        .appointment-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .appointment-details {
            margin-bottom: 25px;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }
        
        .detail-label {
            font-weight: bold;
            color: #555;
            width: 120px;
            flex-shrink: 0;
        }
        
        .detail-value {
            color: #333;
            flex: 1;
        }
        
        .time-display {
            background-color: #e8f4f8;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .appointment-images {
            margin-top: 25px;
        }
        
        .images-header {
            font-size: 18px;
            font-weight: bold;
            color: #4a7c59;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .image-item {
            text-align: center;
        }
        
        .appointment-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .appointment-image:hover {
            transform: scale(1.05);
        }
        
        .no-images {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 20px;
            color: #d32f2f;
            background-color: #ffebee;
            border-radius: 4px;
            border: 1px solid #ffcdd2;
        }
        
        .close-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #4a7c59;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .close-btn:hover {
            background-color: #3a5f47;
        }
        
        .provider-badge {
            background-color: #4a7c59;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-block;
        }
        
        .appointment-location {
            background-color: #f0f8f0;
            padding: 6px 10px;
            border-radius: 4px;
            display: inline-block;
            color: #2d5016;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <button class="close-btn" onclick="window.close()">Close</button>
    
    <div id="appointmentContainer" class="appointment-container">
        <div class="loading">Loading appointment details...</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadAppointmentDetails();
        });
        
        function loadAppointmentDetails() {
            var urlParams = new URLSearchParams(window.location.search);
            var demographicNo = urlParams.get('demographicNo');
            var focusAppointment = urlParams.get('focus');
            
            if (!demographicNo) {
                showError('No demographic number provided');
                return;
            }
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', '<%=request.getContextPath()%>/hm/getPatientAppointments.jsp?demographic_no=' + demographicNo, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var appointments = JSON.parse(xhr.responseText);
                            var appointment = null;
                            
                            if (focusAppointment) {
                                // Find the specific appointment
                                appointment = appointments.find(function(apt) {
                                    return apt.appointment_no === focusAppointment;
                                });
                            }
                            
                            if (!appointment && appointments.length > 0) {
                                // If no specific appointment found, show the first one
                                appointment = appointments[0];
                            }
                            
                            if (appointment) {
                                displayAppointmentDetails(appointment);
                            } else {
                                showError('Appointment not found');
                            }
                        } catch (e) {
                            console.error('Error parsing appointment data:', e);
                            showError('Error loading appointment details');
                        }
                    } else {
                        showError('Error loading appointment details (HTTP ' + xhr.status + ')');
                    }
                }
            };
            xhr.send();
        }
        
        function displayAppointmentDetails(appointment) {
            var container = document.getElementById('appointmentContainer');
            
            var html = '';
            html += '<div class="appointment-header">';
            html += '<div class="appointment-title">' + escapeHtml(appointment.reason || 'Appointment') + '</div>';
            html += '<div class="appointment-subtitle">Appointment #' + escapeHtml(appointment.appointment_no) + '</div>';
            html += '</div>';
            
            html += '<div class="appointment-details">';
            
            // Date
            if (appointment.appointment_date) {
                html += '<div class="detail-row">';
                html += '<div class="detail-label">Date:</div>';
                html += '<div class="detail-value">' + escapeHtml(appointment.appointment_date) + '</div>';
                html += '</div>';
            }
            
            // Time
            if (appointment.start_time || appointment.end_time) {
                html += '<div class="detail-row">';
                html += '<div class="detail-label">Time:</div>';
                html += '<div class="detail-value">';
                html += '<span class="time-display">';
                if (appointment.start_time && appointment.end_time) {
                    html += escapeHtml(appointment.start_time) + ' - ' + escapeHtml(appointment.end_time);
                } else if (appointment.start_time) {
                    html += escapeHtml(appointment.start_time);
                }
                html += '</span>';
                html += '</div>';
                html += '</div>';
            }
            
            // Provider
            if (appointment.provider_name) {
                html += '<div class="detail-row">';
                html += '<div class="detail-label">Provider:</div>';
                html += '<div class="detail-value">';
                html += '<span class="provider-badge">' + escapeHtml(appointment.provider_name) + '</span>';
                html += '</div>';
                html += '</div>';
            }
            
            // Location
            if (appointment.location) {
                html += '<div class="detail-row">';
                html += '<div class="detail-label">Location:</div>';
                html += '<div class="detail-value">';
                html += '<span class="appointment-location">' + escapeHtml(appointment.location) + '</span>';
                html += '</div>';
                html += '</div>';
            }
            
            // Notes
            if (appointment.notes) {
                html += '<div class="detail-row">';
                html += '<div class="detail-label">Notes:</div>';
                html += '<div class="detail-value">' + escapeHtml(appointment.notes) + '</div>';
                html += '</div>';
            }
            
            html += '</div>';
            
            // Images section
            html += '<div class="appointment-images">';
            html += '<div class="images-header">上传的图片</div>';
            
            if (appointment.images && appointment.images.length > 0) {
                html += '<div class="images-grid">';
                appointment.images.forEach(function(imageUrl, index) {
                    html += '<div class="image-item">';
                    html += '<img src="' + escapeHtml(imageUrl) + '" ';
                    html += 'alt="Appointment Image ' + (index + 1) + '" ';
                    html += 'class="appointment-image" ';
                    html += 'onclick="openImageModal(\'' + escapeHtml(imageUrl) + '\')" ';
                    html += 'onerror="this.style.display=\'none\'">';
                    html += '</div>';
                });
                html += '</div>';
            } else {
                html += '<div class="no-images">没有上传的图片</div>';
            }
            
            html += '</div>';
            
            container.innerHTML = html;
        }
        
        function showError(message) {
            var container = document.getElementById('appointmentContainer');
            container.innerHTML = '<div class="error">' + escapeHtml(message) + '</div>';
        }
        
        function escapeHtml(text) {
            if (!text) return '';
            var div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function openImageModal(imageUrl) {
            // Open image in a new window for full view
            var win = window.open('', '_blank');
            win.document.write('<html><head><title>Image Viewer</title></head><body style="margin:0;padding:20px;background:#000;text-align:center;"><img src="' + imageUrl + '" style="max-width:100%;max-height:100vh;"/></body></html>');
        }
    </script>
</body>
</html> 